import { DjInfo, LineInfo } from '#/store';

// 提供json转换器，将后端的json数据转换为前端需要的json数据
export const jsonConvert = (data) => {
  // 转换kbxTableList字段内容：
  const kbxTableListData = data.kbxTableList;
  const menuInfoListData = data.menuInfoList;
  const mxTableListData = data.mxTableList;
  const lineList = data.lineList;

  const djInfo: DjInfo = {
    djlx: data.djlx,
    djbs: data.djbs,
    runTitle: data.runTitle,
    mxActive: data.mxActive,
    tdHeight: data.tdHeight,
    qtHeight: data.qtHeight,
    mxHeight: data.mxHeight,
    kbxTableList: [],
    mxTableList: [],
    menuInfoList: data.menuInfoList,
    lineList: [],
  };

  interface KbxTableResult {
    upMx: any;
    canEdit?: boolean;
    field?: any;
    title?: any;
    X?: number;
    Y?: number;
    W?: number;
    H?: number;
    span?: number;
    fieldName?: string;
    fieldTaborder?: number;
    fieldFontStyle?: string;
    fieldFontSize?: number;
    itemRender?: {
      name: string;
      checkField?: any[];
      enabled?: boolean;
      optionType?: string;
      options?: Array<{ label: string; value: string }>;
    };
    fieldFontColor?: string;
    onChangeFunc: string;
    onDblFunc: string;
    onEnterFunc: string;
    onExitFunc: string;
    onIntiFunc: string;
    isMxField?: boolean;
    fldType?: string | number;
    fldDec?: number;
    fldLength?: number;
    isActive?: boolean;
  }

  interface mxTableResult {
    field: string;
    title: string;
    width: number;
    align: string;
    onDblFunc?: string;
    onEnterFunc?: string;
    onExitFunc?: string;
    canEdit?: boolean;
    editRender?: {
      name: string;
      placeholder?: string;
      events?: { currentChange: () => void };
      autoselect?: boolean;
      immediate?: boolean;
      attrs?: {
        class?: string;
        style?: Record<string, string>;
        type?: string;
        maxlength?: number;
        oninput?: (event: Event) => void;
      };
    };
    resizable?: boolean;
    minWidth?: number;
    datetype?: string;
    fldType?: string | number;
    fldDec?: number;
    fldLength?: number;
    isDict?: boolean;
    dropdownoptions?: Array<{
      name: string;
      value: string;
      sort: number;
    }>;
    isActive?: boolean;
    fldC?: string;
    backC?: string;
  }

  interface DropMenu {
    onClick?: Fn;
    to?: string;
    icon?: string;
    event: string | number;
    text: string;
    disabled?: boolean;
    divider?: boolean;
  }

  interface menuInfoResult {
    menuLx: string;
    fixed: boolean;
    menuBh: string;
    lcTitle: string;
    isActive: boolean;
    functionName?: string;
    spButtonBmp?: Blob;
    sonMenuList?: DropMenu[];
  }

  if (kbxTableListData != null) {
    djInfo.kbxTableList = kbxTableListData
      .map((item) => {
        let kbxTable: KbxTableResult = {
          upMx: item.upMx,
          fieldTaborder: item.fieldTaborder,
          X: item.posX,
          Y: item.posY,
          W: item.fieldWidth,
          H: item.fieldHeight,
          fieldFontStyle: item.fieldFontStyle,
          fieldFontSize: item.fieldFontSize,
          canEdit: item.canEdit,
          fieldName: item.fieldName,
          onChangeFunc: item.onChangeFunc,
          onDblFunc: item.onDblFunc,
          onEnterFunc: item.onEnterFunc,
          onExitFunc: item.onExitFunc,
          onIntiFunc: item.onIntiFunc,
          fieldFontColor: item.fieldFontColor,
          isMxField: item.isMxField,
          fldType: item.fldType,
          fldDec: item.fldDec,
          fldLength: item.fldLength,
          isActive: item.isActive,
        };
        // 判断lx类型
        switch (item.lx) {
          case 'A': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'AText',
              },
            };
            break;
          }
          case 'B': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'AInput',
              },
            };
            break;
          }
          case 'R': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'AButton',
              },
            };
            break;
          }
          case 'S': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'ASum',
              },
            };
            break;
          }
          case 'P': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'APSum',
              },
            };
            break;
          }
          // 如果lx为C则为checkbox
          case 'C': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              X: item.posX,
              Y: item.posY,
              W: item.fieldWidth,
              H: item.fieldHeight,
              itemRender: {
                name: 'ACheckbox',
                optionType: 'button',
              },
            };
            break;
          }
          // 日期类型（YYYY-MM-DD格式）
          case 'D': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'ADatePicker',
                enabled: item.canEdit,
              },
            };
            break;
          }
          // 时间类型（HH:MM:SS格式）
          case 'E': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'ATimePicker',
                enabled: item.canEdit,
              },
            };
            break;
          }
          // 日期时间类型（YYYY-MM-DD HH:MM:SS格式）
          case 'F': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'ADateTimePicker',
                enabled: item.canEdit,
              },
            };
            break;
          }
          case 'L': {
            kbxTable = {
              ...kbxTable,
              field: item.fieldId,
              title: item.labTitle || item.fieldTitle,
              span: 4,
              itemRender: {
                name: 'ASelect',
                enabled: item.canEdit,
                options:
                  item.dropdownoptions?.map((option) => ({
                    label: option.name,
                    value: option.value,
                  })) || [],
              },
            };
            break;
          }
          case 'G': {
            kbxTable = {
              ...kbxTable,
              W: item.fieldWidth,
              H: item.fieldHeight,
              itemRender: {
                name: 'Trichedit',
              },
            };
            break;
          }
          case 'M': {
            kbxTable = {
              ...kbxTable,
              W: item.fieldWidth,
              H: item.fieldHeight,
              itemRender: {
                name: 'Tmemo',
              },
            };
            break;
          }
          default: {
            // 如果没有命中则不返回该数据
            return;
          }
        }
        return kbxTable;
      })
      .filter((item) => item);
  }

  if (menuInfoListData != null) {
    djInfo.menuInfoList = menuInfoListData
      .map((item) => {
        const menuInfo: menuInfoResult = {
          menuLx: item.menuLx,
          fixed: item.fixed,
          menuBh: item.menuBh,
          lcTitle: item.lcTitle,
          isActive: item.isActive,
          functionName: item.functionName,
          spButtonBmp: item.spButtonBmp,
          sonMenuList: [],
        };
        const sonMenuList: DropMenu[] = [];
        if (item.sonMenuList) {
          item.sonMenuList.forEach((sonItem: menuInfoResult) => {
            if (sonItem.isActive) {
              sonMenuList.push({
                to: sonItem.functionName ? undefined : sonItem.menuBh,
                event: sonItem.functionName || sonItem.lcTitle,
                text: sonItem.lcTitle,
                disabled: !sonItem.isActive,
                divider: false,
              });
            }
          });
        }
        menuInfo.sonMenuList = sonMenuList;
        return menuInfo;
      })
      .filter((item) => item);
  }

  if (mxTableListData != null) {
    djInfo.mxTableList = mxTableListData
      .map((item) => {
        const mxTable: mxTableResult = {
          field: item.fieldName,
          width: item.showLength,
          title: item.labTitle || item.fieldTitle,
          align: alignStyleConvert(item.alignStyle),
          onDblFunc: item.onDblFunc,
          onEnterFunc: item.onEnterFunc,
          onExitFunc: item.onExitFunc,
          canEdit: item.canEdit,
          resizable: true,
          minWidth: 0,
          datetype: item.datetype,
          fldType: item.fldType,
          fldDec: item.fldDec,
          fldLength: item.fieldLength,
          isDict: item.isDict,
          dropdownoptions: item.dropdownoptions,
          isActive: item.isActive,
          fldC: item.fldC,
          backC: item.backC,
        };

        if (item.canEdit) {
          mxTable.editRender = {
            name: 'AInput',
            placeholder: '请点击输入',
            autoselect: true,
            immediate: true,
            attrs: {
              class: 'vxe-auto-datainput',
              style: {},
            },
          };

          // 根据字段类型添加输入限制
          if (item.fldType === '1' || item.fldType === 1) {
            // 实数类型
            if (mxTable.editRender && mxTable.editRender.attrs) {
              mxTable.editRender.attrs = {
                ...mxTable.editRender.attrs,
                type: 'text',
                maxlength: item.fieldLength,
                oninput: (event: Event) => {
                  const target = event.target as HTMLInputElement;
                  if (!target) return;

                  const value = target.value;
                  // 只允许输入数字和一个小数点
                  let newValue = value
                    .replace(/[^\d.-]/g, '')
                    .replace(/^-+/, '-') // 只允许一个负号且在开头
                    .replace(/\.+/g, '.'); // 只允许一个小数点

                  // 限制小数位数
                  if (item.fldDec) {
                    const parts = newValue.split('.');
                    if (parts.length > 1 && parts[1].length > item.fldDec) {
                      newValue = `${parts[0]}.${parts[1].substring(0, item.fldDec)}`;
                    }
                  }

                  // 检查总长度（不包含小数点）
                  const strWithoutDot = newValue.replace(/\./g, '').replace(/^-/, '');
                  if (strWithoutDot.length > (item.fieldLength || 0)) {
                    event.preventDefault();
                    return;
                  }

                  if (value !== newValue) {
                    target.value = newValue;
                  }
                },
              };
            }
          } else if (item.fldType === '2' || item.fldType === 2) {
            // 整数类型
            if (mxTable.editRender && mxTable.editRender.attrs) {
              mxTable.editRender.attrs = {
                ...mxTable.editRender.attrs,
                type: 'text',
                maxlength: item.fieldLength,
                oninput: (event: Event) => {
                  const target = event.target as HTMLInputElement;
                  if (!target) return;

                  const value = target.value;
                  // 只允许输入整数
                  const newValue = value.replace(/[^\d-]/g, '').replace(/^-+/, '-'); // 只允许一个负号且在开头

                  // 检查总长度（不包含负号）
                  const strWithoutMinus = newValue.replace(/^-/, '');
                  if (strWithoutMinus.length > (item.fieldLength || 0)) {
                    event.preventDefault();
                    return;
                  }

                  if (value !== newValue) {
                    target.value = newValue;
                  }
                },
              };
            }
          } else if (item.fldType === '3' || item.fldType === 3) {
            // 字符类型
            if (mxTable.editRender && mxTable.editRender.attrs) {
              mxTable.editRender.attrs = {
                ...mxTable.editRender.attrs,
                type: 'text',
                maxlength: item.fieldLength,
              };
            }
          }
        }
        return mxTable;
      })
      .filter((item) => item);
  }

  if (lineList != null) {
    djInfo.lineList = lineList
      .map((item) => {
        const lineInfo: LineInfo = {
          lx: item.lx,
          sposX: item.sposX,
          sposY: item.sposY,
          dposX: item.dposX,
          dposY: item.dposY,
          lineColor: item.lineColor,
          isActive: item.isActive,
          upMx: item.upMx,
          lineWidth: item.lineWidth,
          lrbHeight: item.lrbHeight,
          lrbWidth: item.lrbWidth,
          lrbName: item.lrbName,
          boxImage: item.boxImage,
          bmpAutoSize: item.bmpAutoSize,
          bmpStretch: item.bmpStretch,
        };
        return lineInfo;
      })
      .filter((item) => item);
  }

  return djInfo;
};

// 转换alignStyle
export const alignStyleConvert = (alignStyle: string) => {
  switch (alignStyle) {
    case 'ALLEFT':
      return 'left';
    case 'ALCENTER':
      return 'center';
    case 'ALRIGHT':
      return 'right';
    default:
      return 'left';
  }
};

/**
 * 根据字段类型设置默认值
 * @param value 原始值
 * @param fldType 字段类型
 * @returns 处理后的默认值
 */
export const setDefaultValueByType = (value: any, fldType: string | number): any => {
  // 如果已有值且不为空，直接返回
  if (value !== null && value !== undefined && value !== '') {
    return value;
  }

  // 根据字段类型设置默认值
  if (fldType === '1' || fldType === 1) {
    // 实数类型默认为0
    return 0;
  } else if (fldType === '2' || fldType === 2) {
    // 整数类型默认为0
    return 0;
  }

  // 其他类型默认为null
  return null;
};

/**
 * 根据字段配置格式化数值显示
 * @param value 数值
 * @param fldDec 小数位数配置
 * @returns 格式化后的数值字符串
 */
export const formatNumberByFldDec = (value: any, fldDec?: number): string => {
  // 如果值为null、undefined或空字符串，返回空字符串
  if (value === null || value === undefined || value === '') {
    return '';
  }

  // 转换为数字
  const numValue = Number(value);

  // 如果转换失败，返回原始值
  if (isNaN(numValue)) {
    return String(value);
  }

  // 处理fldDec的边界情况
  let decimalPlaces: number;

  if (fldDec === undefined || fldDec === null) {
    // 如果fldDec未定义，使用默认的2位小数
    decimalPlaces = 2;
  } else if (fldDec === 0) {
    // 如果fldDec为0，显示为整数（无小数点）
    decimalPlaces = 0;
  } else if (fldDec < 0) {
    // 如果fldDec为负数，使用0位小数
    decimalPlaces = 0;
  } else if (fldDec > 10) {
    // 如果fldDec过大，限制为最多10位小数
    decimalPlaces = 10;
  } else {
    // 正常情况，使用配置的小数位数
    decimalPlaces = fldDec;
  }

  // 根据计算得出的小数位数进行格式化
  return numValue.toFixed(decimalPlaces);
};

/**
 * 为数据对象设置数值型字段的默认值
 * @param data 数据对象
 * @param fieldConfigs 字段配置列表
 * @returns 处理后的数据对象
 */
export const setNumericDefaultValues = (
  data: Record<string, any>,
  fieldConfigs: Array<{ fieldName?: string; fldType: string | number }>,
): Record<string, any> => {
  const result = { ...data };

  fieldConfigs.forEach((config) => {
    if (config.fieldName) {
      result[config.fieldName] = setDefaultValueByType(result[config.fieldName], config.fldType);
    }
  });

  return result;
};

/*
使用示例：formatNumberByFldDec函数

// 示例1：fldDec为2时，显示2位小数
formatNumberByFldDec(123.456789, 2) // 返回 "123.46"

// 示例2：fldDec为0时，显示为整数
formatNumberByFldDec(123.456789, 0) // 返回 "123"

// 示例3：fldDec未定义时，使用默认2位小数
formatNumberByFldDec(123.456789) // 返回 "123.46"

// 示例4：fldDec为4时，显示4位小数
formatNumberByFldDec(123.456789, 4) // 返回 "123.4568"

// 示例5：处理空值
formatNumberByFldDec(null, 2) // 返回 ""
formatNumberByFldDec(undefined, 2) // 返回 ""
formatNumberByFldDec("", 2) // 返回 ""

// 示例6：处理负数
formatNumberByFldDec(-123.456789, 2) // 返回 "-123.46"

// 示例7：处理边界情况
formatNumberByFldDec(123.456789, -1) // 返回 "123" (负数fldDec当作0处理)
formatNumberByFldDec(123.456789, 15) // 返回 "123.4567890000" (超过10位限制为10位)
*/
