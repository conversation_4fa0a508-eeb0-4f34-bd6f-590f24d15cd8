<template>
  <PageWrapper contentFullHeight fixedHeight class="mt-0 scroll-smooth">
    <!-- 菜单区域 -->
    <div class="nav-menu-container" @click="handleContainerClick">
      <div class="menu-group">
        <template
          v-for="item in djInfo?.menuInfoList?.filter((i) => i.menuLx == 'M' && i.isActive)"
          :key="item.lcTitle"
        >
          <Dropdown
            v-if="item.sonMenuList && item.sonMenuList.length > 0"
            placement="bottom"
            :trigger="['click']"
            :dropMenuList="item.sonMenuList"
            :selectedKeys="selectedKeys"
            @menu-event="handleMenuEvent"
            overlayClassName="app-locale-picker-overlay"
          >
            <a-button preIcon="ant-design:windows-outlined" type="link">
              {{ item.lcTitle }}
            </a-button>
          </Dropdown>
          <a-button
            v-else
            type="link"
            preIcon="ant-design:select-outlined"
            @click="handlerToolbar(item)"
          >
            {{ item.lcTitle }}
          </a-button>
        </template>
        <!-- 添加清空按钮 -->
        <a-button type="link" preIcon="ant-design:delete-outlined" @click="handleClearAll">
          清空
        </a-button>
      </div>
    </div>

    <!-- 主容器 -->
    <div ref="container" class="invoice-container">
      <!-- 表单区域 -->
      <XFrom
        class="header-section"
        :items="gridOptions.formConfig.items"
        :djInfo="djInfo"
        @jump-to-table="jumpToTableFirstInput()"
        v-model:templateForm="templateStore.templateForm.kbxtable"
        @save-form="saveForm()"
        ref="xFormRef"
        @handle-func="handleFormInputEvent"
        @click="handleContainerClick"
        @change="handleFormChange"
      />

      <!-- 表格区域 -->
      <div class="tables-section">
        <div class="flex">
          <!-- 主表格 -->
          <div
            :class="[sonMxGridOptions ? 'w-3/4 pr-1' : 'w-full', 'main-table']"
            ref="mainTableContainer"
            tabindex="-1"
          >
            <VxeBasicTable
              v-if="djInfo?.mxActive"
              ref="tableRef"
              v-bind="{
                ...gridOptions,
                height: tableHeight,
                showOverflow: true,
                showHeaderOverflow: true,
                scrollY: {
                  enabled: true,
                  gt: 0,
                },
              }"
              v-on="gridEvents"
              @change="handleTableChange"
            >
              <template #form> </template>
              <template #toolbar_buttons>
                <div class="flex items-center justify-between w-full px-2">
                  <div>{{ djInfo.runTitle }}明细</div>
                  <div class="flex items-center gap-2">
                    <template
                      v-for="menu in gridOptions.menuConfig.body.options[0]"
                      :key="menu.code"
                    >
                      <a-button
                        class="mxBtn"
                        style="padding: 4px 2px"
                        type="link"
                        @click="handleMenuClick(menu)"
                      >
                        {{ menu['name'] }}
                      </a-button>
                    </template>
                    <!-- <a-button
                      style="padding: 4px 2px"
                      class="mxBtn"
                      type="link"
                      @click="handleMenuClick({ code: 'showFullScreen' })"
                    >
                      全屏展示
                    </a-button> -->
                    <a-button
                      class="mxBtn"
                      style="padding: 4px 2px"
                      type="link"
                      @click="handleMenuClick({ code: 'showColumn' })"
                    >
                      列设置
                    </a-button>
                  </div>
                </div>
              </template>
              <template
                v-for="column in gridOptions.columns.filter((col) => col.field)"
                :key="`header_${column.field}`"
                #[`header_${column.field}`]
              >
                <div
                  v-for="(option, index) in column.filters"
                  :key="index"
                  class="items-center w-100%"
                >
                  <div
                    class="w-100%"
                    style="position: absolute; top: -6px; right: 0; text-align: right"
                  >
                    <a-button class="mr-2" type="link" size="small" @click="closeFilter()">
                      <template #icon><CloseOutlined style="font-size: 0.5rem" /></template>
                    </a-button>
                  </div>
                  <Input
                    v-model:value="option.data"
                    clearable
                    @change="confirmFilterEvent(option, column)"
                    placeholder="输入筛选"
                    style="width: 100%"
                  />
                </div>
              </template>
              <template #empty>
                <p>没有更多数据了！</p>
              </template>
            </VxeBasicTable>
          </div>

          <!-- 子明细表格 -->
          <div v-if="sonMxGridOptions" class="w-1/4 pl-1 border-l son-mx-table-container">
            <VxeBasicTable
              ref="sonTableRef"
              v-bind="{
                ...sonMxGridOptions,
                height: tableHeight,
              }"
              class="son-mx-table"
            >
              <template #empty>
                <p>没有更多数据了！</p>
              </template>
              <template #toolbar_buttons>
                <div class="flex items-center justify-between w-full px-2"> </div>
              </template>
            </VxeBasicTable>
          </div>
        </div>
        <div style="margin-top: 10px; padding-right: 8px; text-align: right">
          总数：{{ tableRef?.getTableData?.()?.fullData?.length || 0 }}条
        </div>
      </div>
      <!-- 底部区域 -->
      <XFooter
        ref="xFooterRef"
        key="footer"
        :items="gridOptions.footerConfig.items"
        :djInfo="djInfo"
        v-model="templateStore.templateForm.kbxtable"
        :formData="templateStore.templateForm.kbxtable"
        class="footer-section"
        @click="handleContainerClick"
        @sum-change="handleFooterSumChange"
      />
    </div>

    <!-- 子明细表抽屉 -->
    <SonMxDrawer
      ref="sonMxDrawerRef"
      @register="registerDrawer"
      :sonMxGridOptions="sonMxGridOptions ?? {}"
      :sonMxGridEvents="sonMxGridEvents ?? {}"
    />

    <!-- 模态框组件 -->
    <component
      v-if="currentModal"
      :is="currentModal"
      v-model:open="modalOpen"
      :userData="userData"
      @data-choose="handleDataChoose"
    />
    <DjgzListModel
      v-if="djgzModalOpen"
      v-model:open="djgzModalOpen"
      :userData="userData"
      @data-choose="handleDataChoose"
    />
    <ClearGz
      v-if="clearGzModalOpen"
      v-model:open="clearGzModalOpen"
      @quit="quit"
      @new-dj="newDj"
      @print="print"
    />
    <Qldjgz
      v-if="qlDjGzModalOpen"
      v-model:open="qlDjGzModalOpen"
      @data-choose="handleQldjgzChoose"
    />
    <Zljs
      v-if="zljsModalOpen"
      v-model:open="zljsModalOpen"
      :zljsBody="zljsBody"
      @data-choose="zljsDataChoose"
      :mxTableRow="mxTableRow"
      :targetField="currentFieldName"
      :buxszd="buxszd || []"
      :height="600"
      @close="() => (zljsModalOpen = false)"
      :maskClosable="false"
    />
    <CustomColumn
      v-if="customColumnModalOpen"
      v-model:open="customColumnModalOpen"
      :djlx="djlx"
      :customColumnBody="customColumnBody"
      @change-custom-column="changeCustomColumn"
    />
    <a-modal
      v-model:visible="printSchemeModalOpen"
      title="选择打印方案"
      :width="600"
      :footer="false"
      :destroyOnClose="true"
      :maskClosable="false"
    >
      <div class="print-scheme-modal">
        <a-table
          :dataSource="printSchemes"
          :columns="printSchemeColumns"
          rowKey="faid"
          :pagination="false"
          :scroll="{ y: 300 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-button type="primary" @click="() => handlePrintSchemeSelect(record)"
                >打印</a-button
              >
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 资料检索组件 -->
    <zljs
      v-model:visible="zljsModalOpen"
      v-if="zljsModalOpen"
      :zljsBody="zljsBody"
      :targetField="currentFieldName"
      :mxTableRow="mxTableRow"
      :isshy="false"
      :buxszd="buxszd"
      :loading="isDataRetrieving"
      @close="zljsModalOpen = false"
      @data-choose="zljsDataChoose"
    />

    <!-- 勾兑方案组件 -->
    <goudui
      v-model:visible="gouduiModalOpen"
      :gouduiBody="gouduiBody"
      :targetField="currentFieldName || ''"
      :mxTableRow="mxTableRow"
      :buxszd="buxszd"
      @close="gouduiModalOpen = false"
      @data-choose="zljsDataChoose"
      @save="handleGouduiSave"
      @calculate-total="handleCalculateTotal"
    />
    <!-- 资料详情组件 -->
    <ZiliaoDetailsModel
      v-if="ziliaoModalOpen"
      :open="!!ziliaoModalOpen"
      :tableHeader="ziliaoData?.tableheader || []"
      :tableValue="ziliaoData?.tablevalue || {}"
      @cancel="() => (ziliaoModalOpen = false)"
    />

    <!-- 文件管理组件 -->
    <FileManagement
      v-model:visible="fileManagementModalOpen"
      :hzcode="templateStore.djlx"
      :rowid="templateStore.gzbh"
      @success="() => (fileManagementModalOpen = false)"
    />

    <!-- Excel导入组件 -->
    <ExcelImportModal
      v-if="excelImportModalOpen"
      :open="excelImportModalOpen"
      :menuCode="currentExcelImportMenuCode"
      @success="handleExcelImportSuccess"
      @cancel="() => (excelImportModalOpen = false)"
    />
  </PageWrapper>
</template>

<script lang="ts" setup>
  import {
    ref,
    reactive,
    onMounted,
    onActivated,
    nextTick,
    watch,
    onUnmounted,
    h,
    shallowRef,
  } from 'vue';
  import type { ComponentOptions } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import {
    VxeBasicTable,
    VxeGridInstance,
    VxeFormListeners,
    VxeGridListeners,
  } from '@/components/VxeTable';
  import { jsonConvert, formatNumberByFldDec } from './util';
  import {
    templateDetailApi,
    getSonMxDetailApi,
    updateQueryColWidthApi,
    getInitValueApi,
    djDeleteCheckApi,
  } from '@/api/template';
  import { extractRawApi, extractGouduiRawApi } from '@/api/extractRawApi';
  import { Dropdown } from '@/components/Dropdown';
  import { useTabs } from '@/hooks/web/useTabs';
  import Model from './test.vue';
  import DjgzListModel from './components/djgzList.vue';
  import XFrom from './components/XForm.vue';
  import XFooter from './components/XFooter.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { MenuInfo } from '../../../types/store';
  import { useTemplateStore } from '@/store/modules/template';
  import { Modal, Input, message, Modal as AModal, Table as ATable } from 'ant-design-vue';
  import ClearGz from './components/clearGz.vue';
  import Qldjgz from './components/qldjgz.vue';

  import Zljs from './components/zljs.vue';
  import CustomColumn from './components/customColumn.vue';
  import { debounce } from 'lodash-es';
  import { CloseOutlined } from '@ant-design/icons-vue';
  import SonMxDrawer from './components/SonMxDrawer.vue';
  import { useDrawer } from '@/components/Drawer';
  import { getPrintSchemes, exportSvgZip, PrintSchemeVo } from '@/api/system';
  // 导入JSZip
  import jszip from 'jszip';
  // 在import部分添加dayjs导入
  import { dateUtil } from '@/utils/dateUtil';
  import goudui from './components/goudui.vue';
  import ZiliaoDetailsModel from '../datacard/components/ZiliaoDetailsModel.vue';
  import { queryArchiveDataApi } from '@/api/datacard';
  import FileManagement from '../datacard/components/FileManagement.vue';
  import ExcelImportModal from './components/ExcelImportModal.vue';
  import { CurrencyFormatter } from '@/utils/currency';

  const dataChooseOption = ref<string>('');

  const { closeCurrent, setTitle } = useTabs();
  const router = useRouter();

  const { query } = useRoute();

  const currentModal = shallowRef<Nullable<ComponentOptions>>(null);

  const templateStore = useTemplateStore();

  const selectedKeys = ref<string[]>([]);
  const userData = ref<any>(null);
  const modalOpen = ref<Boolean>(false);
  const clearGzModalOpen = ref<Boolean>(false);
  const djgzModalOpen = ref<Boolean>(false);
  const qlDjGzModalOpen = ref<Boolean>(false);
  const zljsModalOpen = ref<Boolean>(false);
  const customColumnModalOpen = ref<Boolean>(false);
  const ziliaoModalOpen = ref<Boolean>(false);
  const ziliaoData = ref<any>(null);
  const fileManagementModalOpen = ref<Boolean>(false);
  const excelImportModalOpen = ref<Boolean>(false);
  const currentExcelImportMenuCode = ref<string>('');
  const tableRef = ref<VxeGridInstance>();
  const sonTableRef = ref<VxeGridInstance>();
  const djInfo = ref<any>(null);
  const xFormRef = ref<any>();
  const xFooterRef = ref<any>();
  const container = ref<HTMLElement | null>(null);
  const firstLoad = ref<Boolean>(true);
  const addedFilter = ref<Boolean>(false);
  const djEventType = ref<any>(1);
  const handleFuncName = ref<string>('');
  const djbs = ref<string>('');
  const djlx = ref<string>('');
  const zljsBody = ref<any>(null);
  const buxszd = ref<any>(null);
  const mxTableRow = ref<any>(null);
  const customColumnBody = ref<any>(null);
  const lastClickColumn = ref<any>(null);
  const sonMxTable = ref<any>(null);
  const sonMxGridOptions = ref<any>(null);
  const sonMxDrawerRef = ref<any>(null);

  const currentFieldName = ref<string>('');

  const preGzbh = ref<string>('');

  const tableHeight = ref<number>(0);

  // 抽屉相关
  const [registerDrawer, { openDrawer: openSonMxDrawer }] = useDrawer();

  const sonMxGridEvents = ref<any>(null);

  const mainTableContainer = ref<HTMLElement | null>(null);

  const isValidating = ref(false);
  const modalValidated = ref(false);

  // 添加校验锁，防止重复校验
  const validationLock = ref(new Set());
  // 存储当前活跃的Modal实例，用于页面切换时清理
  const activeModals = ref(new Set());

  // 添加数据检索状态管理
  const isDataRetrieving = ref(false);
  const dataRetrievalLock = ref(false);

  // 新增：当前编辑位置状态管理
  const currentEditPosition = ref<{
    rowIndex: number | null;
    fieldName: string | null;
    row: any | null;
  }>({
    rowIndex: null,
    fieldName: null,
    row: null,
  });

  // 新增：记录上一次编辑的位置
  const previousEditPosition = ref<{
    rowIndex: number | null;
    fieldName: string | null;
    row: any | null;
  }>({
    rowIndex: null,
    fieldName: null,
    row: null,
  });

  // 在data部分添加打印方案相关的数据
  const printSchemeModalOpen = ref<boolean>(false);
  const printSchemes = ref<PrintSchemeVo[]>([]);
  const printSchemeColumns = [
    { title: '方案名称', dataIndex: 'faname', key: 'faname' },
    { title: '操作', key: 'action', width: 100 },
  ];

  const cachedEventType = ref<any>(null); // 添加缓存，用来保存用户操作的2或3

  // 添加一个防止回车事件重复触发的标志
  const enterEventLock = ref(false);

  const gouduiModalOpen = ref(false);
  const gouduiBody = ref<any>({});

  // 添加一个Map来记录每个gzbh对应的勾兑方式
  const gouduiMethodMap = ref(new Map());

  // 存储保存单据后返回的挂账编号，用于打印功能
  const savedGzbh = ref<string>('');

  // 处理Excel导入成功
  function handleExcelImportSuccess(importResult: any) {
    try {
      // 获取表格实例
      const gridInstance = tableRef.value;
      if (!gridInstance) {
        message.error('无法获取表格实例');
        return;
      }

      // 从importResult.data中获取数据，如果不存在data属性则直接使用importResult
      const resultData = importResult?.data || importResult;
      const { sucData = [], failData = [] } = resultData;
      let currentData = gridInstance.getTableData().fullData;
      // 清空现有数据，实现覆盖效果
      gridInstance.remove(currentData);
      // 过滤空行的函数
      const isNotEmpty = (item: any) => {
        // 检查对象是否有有效的非空值
        if (!item || typeof item !== 'object') return false;

        // 检查是否所有字段都为空
        const values = Object.values(item);
        return values.some((value) => {
          if (value === null || value === undefined) return false;
          if (typeof value === 'string') return value.trim() !== '';
          if (typeof value === 'number') return !isNaN(value);
          return true;
        });
      };

      // 将成功的数据插入到表格中，过滤掉空行
      sucData.filter(isNotEmpty).forEach((item: any) => {
        gridInstance.insertAt(item, -1);
      });

      // 将失败的数据也插入到表格中，可以添加一个标识字段，同样过滤空行
      failData.filter(isNotEmpty).forEach((item: any) => {
        // 为失败数据添加错误标识
        const failItem = {
          ...item,
          _importStatus: 'failed', // 添加导入状态标识
          _errorMessage: item.errorMessage || '导入失败', // 如果有错误信息的话
        };
        gridInstance.insertAt(failItem, -1);
      });

      // 删除第一行数据
      // currentData = gridInstance.getTableData().fullData;
      // if (currentData && currentData.length > 0) {
      //   gridInstance.remove(currentData[0]);
      // }

      // 关闭模态框
      excelImportModalOpen.value = false;

      const totalCount = sucData.length + failData.length;
      const successCount = sucData.length;
      const failCount = failData.length;

      // 更新表格中所有行的dj_rec值
      updateRowDjRec();

      if (failCount > 0) {
        message.warning(
          `导入完成：成功 ${successCount} 条，失败 ${failCount} 条，共计 ${totalCount} 条数据`,
        );
      } else {
        message.success(`成功导入 ${successCount} 条数据`);
      }
    } catch (error) {
      console.error('导入数据到表格失败:', error);
      message.error('导入数据到表格失败');
    }
  }

  // 处理勾兑中计算总额的方法
  function handleCalculateTotal(data) {
    // 获取需要更新的字段和总金额
    const { fields, totalValue, targetField } = data;

    // 获取表格实例
    const gridInstance = tableRef.value;
    if (!gridInstance) return;

    // 获取当前表格数据
    const tableData = gridInstance.getTableData().fullData;

    // 如果表格没有数据，创建一行新数据
    if (!tableData || tableData.length === 0) {
      // 创建新行
      const newRow = {};

      // 设置所有字段的值
      fields.forEach((field) => {
        if (typeof field === 'string') {
          newRow[field] = totalValue;
        }
      });

      // 添加行到表格
      gridInstance.insertAt(newRow, -1);

      // 如果有自定义的字段更新逻辑，执行它
      if (targetField) {
        newRow[targetField] = totalValue;
      }
    } else {
      // 更新第一行数据
      const firstRow = tableData[0];

      // 设置指定字段的值
      fields.forEach((field) => {
        if (typeof field === 'string') {
          firstRow[field] = totalValue;
        }
      });

      // 如果有自定义的字段更新逻辑，执行它
      if (targetField) {
        firstRow[targetField] = totalValue;
      }

      // 更新表格数据
      gridInstance.reloadData([firstRow]);
    }

    // 关闭勾兑对话框
    gouduiModalOpen.value = false;
  }

  // 从localStorage加载勾兑方式记录
  function loadGouduiMethodsFromStorage() {
    try {
      const storedData = localStorage.getItem('goudui_methods');
      if (storedData) {
        const methodsData = JSON.parse(storedData);
        gouduiMethodMap.value = new Map(Object.entries(methodsData));
      }
    } catch (error) {
      console.error('加载勾兑方式数据失败:', error);
    }
  }

  // 保存勾兑方式到localStorage
  function saveGouduiMethodsToStorage() {
    try {
      // 将Map转换为对象
      const methodsObj = Object.fromEntries(gouduiMethodMap.value.entries());
      localStorage.setItem('goudui_methods', JSON.stringify(methodsObj));
    } catch (error) {
      console.error('保存勾兑方式数据失败:', error);
    }
  }

  // 在mounted时加载localStorage中的勾兑方式记录
  onMounted(() => {
    loadGouduiMethodsFromStorage();
  });

  function computedTableHeight() {
    const totalHeight = container.value?.offsetHeight ?? 0;
    const formHeight = (djInfo.value?.tdHeight ?? 0) + 16; // 加上margin和padding
    const footerHeight = 50; // 120 + margin + padding

    // 计算主表格可用高度 = 总高度 - 表单高度 - 子表格高度 - 底部高度 - 额外padding
    const availableHeight = totalHeight - formHeight - footerHeight - 55;
    return Math.max(availableHeight, 100); // 确保最小高度为100px
  }

  // 调用接口获取模板数据并使用DjInfo接收
  const formEvents: VxeFormListeners = {
    submit() {
      console.log('form submit');
    },
    reset() {
      console.log('form reset');
    },
  };

  // 创建一个防重复校验的函数
  const _debouncedValidateField = debounce(async (params, callback) => {
    // 创建校验锁的唯一标识
    const lockKey = `${params.fieldName}_${params.rowIndex}_${params.fieldValue}`;

    // 如果正在校验相同字段，则跳过
    if (validationLock.value.has(lockKey)) {
      return;
    }

    // 添加校验锁
    validationLock.value.add(lockKey);

    try {
      const validateResult = await templateStore.validateField(params);
      if (callback) {
        callback(validateResult);
      }
    } finally {
      // 校验完成后移除锁
      validationLock.value.delete(lockKey);
    }
  }, 300);

  // 添加初始化标志，防止重复初始化
  const isInitializing = ref(false);

  // 检查并确保kbxtable已初始化的函数
  const ensureKbxtableInitialized = async () => {
    // 如果正在初始化，等待完成
    if (isInitializing.value) {
      // 等待初始化完成，最多等待5秒
      let waitCount = 0;
      while (isInitializing.value && waitCount < 50) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        waitCount++;
      }
      return;
    }

    // 检查kbxtable是否为空或空对象
    const kbxtable = templateStore.templateForm.kbxtable;
    const isEmpty =
      !kbxtable ||
      kbxtable === null ||
      kbxtable === undefined ||
      (typeof kbxtable === 'object' && Object.keys(kbxtable).length === 0);

    if (isEmpty) {
      try {
        isInitializing.value = true;
        console.log('🔄 [DEBUG] kbxtable为空，开始初始化...');

        await templateStore.initGz({
          djlx: djlx.value + '',
          djbs: djbs.value,
          kbxTableData: {},
          mxTableDataList: [],
        });

        console.log('✅ [DEBUG] kbxtable初始化完成');
      } catch (error) {
        console.error('❌ [ERROR] kbxtable初始化失败:', error);
        throw error;
      } finally {
        isInitializing.value = false;
      }
    }
  };

  // 获取字段值的辅助函数：先从明细行获取，如果获取不到则从汇总项获取
  const getFieldValue = async (row: any, fieldName: string) => {
    // 先尝试获取明细行的字段数据
    const mxValue = row[fieldName];

    // 如果明细行字段值不为空、null、undefined，则使用明细行的值
    if (mxValue !== null && mxValue !== undefined && mxValue !== '') {
      return mxValue;
    }

    // 确保kbxtable已初始化
    await ensureKbxtableInitialized();

    // 如果明细行字段值为空，则获取汇总项该字段数据的值
    const hzValue = templateStore.templateForm.kbxtable[fieldName];
    return hzValue || '';
  };

  // 构建校验参数
  const buildValidateParams = async (
    row: any,
    column: any,
    rowIndex: number,
    isManualEdit: boolean = true,
  ) => {
    let fieldValue = await getFieldValue(row, column.field);
    // 统一处理：数字字段为空值时传递给后端时设为0，保持后端逻辑不变
    if (
      (column.fldType === '1' || column.fldType === '2') &&
      (fieldValue === '' || fieldValue === null || fieldValue === undefined)
    ) {
      fieldValue = 0;
    }

    return {
      fieldName: column.field,
      fieldValue,
      rowIndex,
      mxtable: templateStore.templateForm.mxtable,
      dspid: row.dspid || '',
      dkfid: row.dkfid || '',
      jwh: row.jwh || '',
      miejph: row.miejph || '',
      hescbj: row.hescbj || '',
      hsgscb: row.hsgscb || '',
      picih: row.picih || '',
      ddwid: row.ddwid || templateStore.templateForm.kbxtable.ddwid || '',
      dj_rec: row.dj_rec || rowIndex + 1,
      is_zzs: row.is_zzs || '否',
      beizhu: row.beizhu || '',
      beizhu1: row.beizhu1 || '',
      beizhu2: row.beizhu2 || '',
      xgdj_rec: row.xgdj_rec || '',
      yuandjbh: row.yuandjbh || '',
      xgdjbh: row.xgdjbh || '',
      fus: row.fus || '1',
      dzyid: row.dzyid || '',
      appver: row.appver || '',
      P_dfbs: row.P_dfbs || '',
      gjkl: row.gjkl || '',
      isManualEdit,
      validationMode: 'cell-switch', // 默认验证模式为输入框间切换
    };
  };

  // 检查行是否为空（没有任何有效数据）
  const isRowEmpty = (row: any) => {
    if (!row) return true;

    // 检查该行是否为空行（除了内部字段外都是null、undefined或空字符串）
    return Object.keys(row).every((key) => {
      // 排除内部字段：_X_ROW_KEY 是表格内部标识，dj_rec 是系统自动生成的行号
      // 这些字段不算作用户编辑的数据
      if (key === '_X_ROW_KEY' || key === 'dj_rec') {
        return true;
      }
      const value = row[key];
      // 只有当值为null、undefined或空字符串时才认为该字段为空
      return value === null || value === undefined || value === '' || value === 0;
    });
  };

  // 检查表格是否存在空行
  const hasEmptyRow = () => {
    const tableData = tableRef.value?.getTableData().fullData;
    if (!tableData || tableData.length === 0) return false;

    // 检查是否有任何一行为空行
    return tableData.some((row) => isRowEmpty(row));
  };

  // 更新表格中所有行的dj_rec值
  const updateRowDjRec = () => {
    const tableData = tableRef.value?.getTableData().fullData;
    if (!tableData) return;

    // 遍历表格数据，为每一行设置正确的dj_rec值
    tableData.forEach((row, index) => {
      row.dj_rec = index + 1;

      // 确保同步更新相关字段，防止API调用时出现不一致
      // 有些接口使用zg1_rev或其他字段来识别行
      if (row.zg1_rev !== undefined) {
        row.zg1_rev = row.dj_rec;
      }
      if (row.dj_rev !== undefined) {
        row.dj_rev = row.dj_rec;
      }
    });

    // 输出调试信息
    console.log(
      '更新行号 - 已更新所有行的dj_rec值:',
      tableData.map((row) => ({
        dj_rec: row.dj_rec,
        zg1_rev: row.zg1_rev,
        dj_rev: row.dj_rev,
      })),
    );
  };

  // 确保表格至少有一个空行
  const ensureAtLeastOneEmptyRow = () => {
    const tableData = tableRef.value?.getTableData().fullData;

    // 如果表格完全没有数据，添加一个空行
    if (!tableData || tableData.length === 0) {
      const emptyRow = {};
      // 为明细表的字段设置默认值
      djInfo.value.mxTableList?.forEach((item) => {
        if (item.fldType === '1' || item.fldType === 1) {
          // 实数类型默认为null，在提交时转换为0
          emptyRow[item.field] = null;
        } else if (item.fldType === '2' || item.fldType === 2) {
          // 整数类型默认为null，在提交时转换为0
          emptyRow[item.field] = null;
        } else {
          // 其他类型默认为null
          emptyRow[item.field] = null;
        }
      });
      tableRef.value?.loadData([emptyRow]);
      console.log('📝 [DEBUG] 表格为空，添加默认空行');
      return;
    }

    // 检查是否有空行
    const hasEmptyRowInTable = tableData.some((row) => isRowEmpty(row));

    // 如果表格有数据但没有空行，添加一个空行
    if (!hasEmptyRowInTable) {
      const emptyRow = {};
      // 为明细表的字段设置默认值
      djInfo.value.mxTableList?.forEach((item) => {
        if (item.fldType === '1' || item.fldType === 1) {
          // 实数类型默认为null，在提交时转换为0
          emptyRow[item.field] = null;
        } else if (item.fldType === '2' || item.fldType === 2) {
          // 整数类型默认为null，在提交时转换为0
          emptyRow[item.field] = null;
        } else {
          // 其他类型默认为null
          emptyRow[item.field] = null;
        }
      });
      tableRef.value?.insertAt(emptyRow, -1);
      console.log('📝 [DEBUG] 表格无空行，添加默认空行');
    }
  };

  // 跳转到第一个空行的第一个输入框
  const jumpToFirstEmptyRow = () => {
    const tableData = tableRef.value?.getTableData().fullData;
    if (!tableData || tableData.length === 0) return;

    // 查找第一个空行
    const firstEmptyRow = tableData.find((row) => isRowEmpty(row));
    if (!firstEmptyRow) return;

    // 获取第一个可编辑的列
    const firstEditableColumnIndex = gridOptions.columns.findIndex(
      (col: any) => col.editRender?.name === 'AInput',
    );

    if (firstEditableColumnIndex !== -1) {
      // 跳转到第一个空行的第一个可编辑单元格
      nextTick(() => {
        tableRef.value?.setEditCell(
          firstEmptyRow,
          gridOptions.columns[firstEditableColumnIndex].field,
        );
      });
    }
  };

  // 处理表格导航
  const handleTableNavigation = async (row: any, column: any) => {
    // 获取当前字段配置
    const djInfo = templateStore.djInfo || {};
    const mxTableList = (djInfo as any).mxTableList || [];

    const currentField = mxTableList.find((field: any) => field.fieldName === column.field);

    // 如果当前字段的nextFldName是keydown，则创建新行并选中第一个输入框
    if (currentField && currentField.nextFldName === 'keydown') {
      // 检查当前行是否为空行，如果是空行则不新增
      if (isRowEmpty(row)) {
        return;
      }

      // 检查是否已存在其他空行，如果存在则跳转到空行的第一个输入框
      const tableData = tableRef.value?.getTableData().fullData;
      const hasEmptyRowInTable = tableData && tableData.some((row) => isRowEmpty(row));
      if (hasEmptyRowInTable) {
        jumpToFirstEmptyRow();
        return;
      }

      // 添加新行
      tableRef.value?.insertAt({}, -1);

      // 更新所有行的dj_rec值
      updateRowDjRec();

      // 获取新行和第一个可编辑的字段
      const newRow = tableRef.value?.getTableData().fullData.slice(-1)[0];
      const firstEditableColumnIndex = gridOptions.columns.findIndex(
        (col: any) => col.editRender?.name === 'AInput',
      );

      if (firstEditableColumnIndex !== -1) {
        // 设置焦点到新行的第一个可编辑单元格
        nextTick(() => {
          tableRef.value?.setEditCell(newRow, gridOptions.columns[firstEditableColumnIndex].field);
        });
      }

      // 明细项换行时调用挂账
      try {
        const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
        await templateStore.saveGzToServer(storageKey);
      } catch (error) {
        console.error('明细项换行时挂账失败:', error);
      }

      return;
    }

    // 获取最后一个可编辑的字段
    const lastEditableColumn = gridOptions.columns
      .filter((col: any) => col?.editRender?.name === 'AInput')
      .slice(-1)[0];

    // 判断是否是最后一个可编辑的字段
    const isLastEditableColumn = column.field === lastEditableColumn.field;

    // 判断是否是最后一行
    const fullData = tableRef.value?.getTableData().fullData;
    const isLastRow = fullData ? fullData.indexOf(row) === fullData.length - 1 : false;

    if (isLastEditableColumn && isLastRow) {
      // 检查当前行是否为空行，如果是空行则不新增
      if (isRowEmpty(row)) {
        return;
      }

      // 检查是否已存在其他空行，如果存在则跳转到空行的第一个输入框
      const tableData2 = tableRef.value?.getTableData().fullData;
      const hasEmptyRowInTable2 = tableData2 && tableData2.some((row) => isRowEmpty(row));
      if (hasEmptyRowInTable2) {
        jumpToFirstEmptyRow();
        return;
      }

      // 添加新行
      tableRef.value?.insertAt({}, -1);

      // 更新所有行的dj_rec值
      updateRowDjRec();

      // 获取新行
      const newRow = tableRef.value?.getTableData().fullData.slice(-1)[0];
      const firstEditableColumnIndex = gridOptions.columns.findIndex((col: any) => col.editRender);
      if (firstEditableColumnIndex !== -1) {
        // 确保焦点设置到新行的第一个可编辑单元格
        nextTick(() => {
          tableRef.value?.setEditCell(newRow, gridOptions.columns[firstEditableColumnIndex].field);
        });
      }

      // 明细项换行时调用挂账
      try {
        const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
        await templateStore.saveGzToServer(storageKey);
      } catch (error) {
        console.error('明细项换行时挂账失败:', error);
      }

      return;
    }

    // 获取当前行和列的索引
    const currentRowIndex = tableRef.value?.getTableData().fullData.indexOf(row);
    const columnIndex = gridOptions.columns.findIndex((col: any) => col.field === column.field);

    // 找到下一个可编辑的列
    let nextEditableColumnIndex = columnIndex + 1;
    while (
      nextEditableColumnIndex < gridOptions.columns.length &&
      (!gridOptions.columns[nextEditableColumnIndex].editRender ||
        gridOptions.columns[nextEditableColumnIndex].editRender.name !== 'AInput')
    ) {
      nextEditableColumnIndex++;
    }

    if (nextEditableColumnIndex < gridOptions.columns.length) {
      // 移动到同一行的下一列可编辑单元格
      nextTick(() => {
        tableRef.value?.setEditCell(row, gridOptions.columns[nextEditableColumnIndex].field);
      });
      return;
    } else if (currentRowIndex < tableRef.value?.getTableData().fullData.length - 1) {
      // 如果是行的最后一列，移动到下一行的第一个可编辑单元格
      const nextRow = tableRef.value?.getTableData().fullData[currentRowIndex + 1];
      const firstEditableColumnIndex = gridOptions.columns.findIndex((col: any) => col.editRender);
      if (firstEditableColumnIndex !== -1) {
        nextTick(() => {
          tableRef.value?.setEditCell(nextRow, gridOptions.columns[firstEditableColumnIndex].field);
        });
      }
      return;
    }
  };

  // 处理校验结果
  const handleValidationResult = (
    validateResult: any,
    row: any,
    column: any,
    callback?: Function,
  ) => {
    // 如果校验不通过，保持在当前字段
    if (!validateResult.valid) {
      const modal = Modal.warn({
        title: '数据校验错误',
        content: h('div', {
          innerHTML: validateResult.errors.join('<br/>'),
        }),
        okText: '确定',
        keyboard: false,
        maskClosable: false,
        onOk: () => {
          // 从活跃Modal集合中移除
          activeModals.value.delete(modal);
          // 确保焦点返回到当前单元格
          setTimeout(() => {
            if (validateResult.pass == '-1') {
              // 拦截，光标不移动，确保返回当前单元格
              isValidating.value = false;

              // 根据验证模式决定处理方式
              if (validateResult.validationMode === 'cell-switch') {
                // 输入框间切换：阻止焦点切换，将焦点重新定位到验证失败的输入框
                tableRef.value?.setEditCell(row, column.field);
              } else if (validateResult.validationMode === 'cell-blur') {
                // 输入框失焦：如果处于拦截状态，则清空该输入框的值
                const fieldConfig = Object.values(templateStore.djInfo?.mxTableList || {}).find(
                  (config: any) => config.fieldName === column.field,
                );

                if (fieldConfig) {
                  if (fieldConfig.fldType === '1' || fieldConfig.fldType === '2') {
                    // 数值类型字段设置为 null
                    row[column.field] = null;
                  } else {
                    // 字符串类型字段设置为空字符串
                    row[column.field] = '';
                  }
                }
              }
            } else if (validateResult.pass == '1') {
              // 不拦截，允许继续，但需要传入true表示校验通过，这样可以保留用户输入的值
              isValidating.value = false;
              if (callback) callback(true);
            } else if (validateResult.pass == '2') {
              // 直接通过，继续下一步
              isValidating.value = false;
              if (callback) callback(true);
            } else {
              // 默认情况（包括重复值校验失败），重置状态并返回当前单元格
              isValidating.value = false;

              // 根据验证模式决定处理方式
              if (validateResult.validationMode === 'cell-switch') {
                // 输入框间切换：阻止焦点切换，将焦点重新定位到验证失败的输入框
                tableRef.value?.setEditCell(row, column.field);
              }
            }
          }, 0);
        },
      });

      // 添加到活跃Modal集合中
      activeModals.value.add(modal);

      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter') {
          modal.destroy();
          // 从活跃Modal集合中移除
          activeModals.value.delete(modal);
          document.removeEventListener('keydown', handleKeyDown);
          // 确保焦点返回到当前单元格
          setTimeout(() => {
            // 回调函数
            if (validateResult.pass == '-1') {
              // 拦截，光标不移动，确保返回当前单元格
              isValidating.value = false;

              // 根据验证模式决定处理方式
              if (validateResult.validationMode === 'cell-switch') {
                // 输入框间切换：阻止焦点切换，将焦点重新定位到验证失败的输入框
                tableRef.value?.setEditCell(row, column.field);
              } else if (validateResult.validationMode === 'cell-blur') {
                // 输入框失焦：如果处于拦截状态，则清空该输入框的值
                const fieldConfig = Object.values(templateStore.djInfo?.mxTableList || {}).find(
                  (config: any) => config.fieldName === column.field,
                );

                if (fieldConfig) {
                  if (fieldConfig.fldType === '1' || fieldConfig.fldType === '2') {
                    // 数值类型字段设置为 null
                    row[column.field] = null;
                  } else {
                    // 字符串类型字段设置为空字符串
                    row[column.field] = '';
                  }
                }
              }
            } else if (validateResult.pass == '1') {
              // 不拦截，允许继续，但需要传入true表示校验通过，这样可以保留用户输入的值
              isValidating.value = false;
              if (callback) callback(true);
            } else if (validateResult.pass == '2') {
              // 直接通过，继续下一步
              isValidating.value = false;
              if (callback) callback(true);
            } else {
              // 默认情况（包括重复值校验失败），重置状态并返回当前单元格
              isValidating.value = false;

              // 根据验证模式决定处理方式
              if (validateResult.validationMode === 'cell-switch') {
                // 输入框间切换：阻止焦点切换，将焦点重新定位到验证失败的输入框
                tableRef.value?.setEditCell(row, column.field);
              }
            }
          }, 0);
        }
      };
      document.addEventListener('keydown', handleKeyDown);
      return;
    }
    // 处理后端接口校验结果
    if (validateResult.pass) {
      if (validateResult.title && validateResult.title.length > 0) {
        const modal = Modal.warn({
          title: '数据校验错误',
          content: h('div', {
            innerHTML: validateResult.title.replace(/\r/g, '<br/>'),
          }),
          okText: '确定',
          keyboard: false,
          maskClosable: false,
          onOk: () => {
            // 从活跃Modal集合中移除
            activeModals.value.delete(modal);
            setTimeout(() => {
              if (validateResult.pass === -1) {
                // 拦截，光标不移动，确保返回当前单元格
                isValidating.value = false;
                // 设置默认值，数值类型统一设置为空值
                const fieldConfig = Object.values(templateStore.djInfo?.mxTableList || {}).find(
                  (config: any) => config.fieldName === column.field,
                );
                if (fieldConfig) {
                  if (Number(fieldConfig.fldType) === 1 || Number(fieldConfig.fldType) === 2) {
                    // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
                    row[column.field] = null;
                  } else {
                    // 字符串类型字段设置为空字符串
                    row[column.field] = '';
                  }
                }

                // 根据验证模式决定处理方式
                if (validateResult.validationMode === 'cell-switch') {
                  // 输入框间切换：阻止焦点切换，将焦点重新定位到验证失败的输入框
                  tableRef.value?.setEditCell(row, column.field);
                }
              } else if (validateResult.pass === 1) {
                // 不拦截，允许继续，但需要传入true表示校验通过，这样可以保留用户输入的值
                if (callback) callback(true);
              } else if (validateResult.pass === 2) {
                // 直接通过，不提示，继续下一步
                if (callback) callback(true);
              }
            }, 0);
          },
        });

        // 添加到活跃Modal集合中
        activeModals.value.add(modal);

        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === 'Enter') {
            modal.destroy();
            // 从活跃Modal集合中移除
            activeModals.value.delete(modal);
            document.removeEventListener('keydown', handleKeyDown);
            console.log('validateResult', validateResult);
            setTimeout(() => {
              if (validateResult.pass === -1) {
                // 拦截，光标不移动，确保返回当前单元格
                isValidating.value = false;
                // 设置默认值，数值类型统一设置为空值
                const fieldConfig = Object.values(templateStore.djInfo?.mxTableList || {}).find(
                  (config: any) => config.fieldName === column.field,
                );
                if (fieldConfig) {
                  if (Number(fieldConfig.fldType) === 1 || Number(fieldConfig.fldType) === 2) {
                    // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
                    row[column.field] = null;
                  } else {
                    // 字符串类型字段设置为空字符串
                    row[column.field] = '';
                  }
                }

                // 根据验证模式决定处理方式
                if (validateResult.validationMode === 'cell-switch') {
                  // 输入框间切换：阻止焦点切换，将焦点重新定位到验证失败的输入框
                  tableRef.value?.setEditCell(row, column.field);
                }
              } else if (validateResult.pass === 1) {
                // 不拦截，允许继续，但需要传入true表示校验通过，这样可以保留用户输入的值
                if (callback) callback(true);
              } else if (validateResult.pass === 2) {
                // 直接通过，不提示，继续下一步
                if (callback) callback(true);
              }
            }, 0);
          }
        };
        document.addEventListener('keydown', handleKeyDown);
        return;
      }
    }

    // 校验通过，执行后续操作
    if (callback) callback(true);

    // 确保dj_rec值正确
    updateRowDjRec();
  };

  const gridEvents: VxeGridListeners = {
    cellDblclick({ column }) {
      if (column.title !== '商品编号') return;
      currentModal.value = Model as ComponentOptions;
      modalOpen.value = true;
    },
    cellClick() {
      // 预留用于后续功能扩展
    },
    // 添加编辑激活事件，当进入编辑状态时自动选中当前行并记录编辑位置
    editActived({ row, column, rowIndex }) {
      if (row) {
        tableRef.value?.setCheckboxRow(row, true);
      }

      // 记录当前编辑位置
      if (column && column.field) {
        currentEditPosition.value = {
          rowIndex,
          fieldName: column.field,
          row,
        };
      }
    },

    async menuClick({ menu, row }) {
      console.log('menu.code', menu.code);
      let menuNameLower = menu.code?.toLowerCase() ?? '';
      switch (menuNameLower) {
        case 'menudel':
          // 判断是否有选择行
          const checkedData = tableRef.value?.getCheckboxRecords();
          const tableData = tableRef.value?.getTableData().fullData;

          // 如果没有选中任何行，提示用户并返回
          if (!checkedData || checkedData.length === 0) {
            // 保存当前活跃元素
            const activeElement = document.activeElement as HTMLElement;
            const modal = Modal.warn({
              title: '提示',
              content: '请先选择要删除的行',
              onOk: () => {
                // 延迟一帧后将焦点返回到之前的元素
                setTimeout(() => {
                  if (activeElement) {
                    activeElement.focus();
                  }
                }, 0);
              },
            });

            // 添加自定义键盘事件处理
            const handleKeyDown = (e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                modal.destroy();
                document.removeEventListener('keydown', handleKeyDown);
                // 延迟一帧后将焦点返回到之前的元素
                setTimeout(() => {
                  if (activeElement) {
                    activeElement.focus();
                  }
                }, 0);
              }
            };
            document.addEventListener('keydown', handleKeyDown);
            return;
          }

          // 分离空行和非空行
          const emptyRows: any[] = [];
          const nonEmptyRows: any[] = [];

          checkedData.forEach((row) => {
            // 检查该行是否为空行（除了内部字段外都是null、undefined或空字符串）
            const isEmpty = Object.keys(row).every((key) => {
              // 排除内部字段：_X_ROW_KEY 是表格内部标识，dj_rec 是系统自动生成的行号
              // 这些字段不算作用户编辑的数据
              if (key === 'dj_rec' || key === '_X_ROW_KEY') return true;
              const value = row[key];
              // 只有当值为null、undefined或空字符串时才认为该字段为空
              // 数字0也算作有效数据，不应该被认为是空
              return value === null || value === undefined || value === '';
            });

            if (isEmpty) {
              emptyRows.push(row);
            } else {
              nonEmptyRows.push(row);
            }
          });

          // 如果有空行，直接删除
          if (emptyRows.length > 0) {
            await performDelete(emptyRows, null, tableRef.value?.getTableData().fullData);
          }

          // 如果没有非空行需要校验，直接返回
          if (nonEmptyRows.length === 0) {
            return;
          }

          // 构建校验请求数据（只对非空行进行校验）
          const checkData = nonEmptyRows.map((item) => {
            // 获取当前行在表格中的实际索引
            const actualRowIndex = tableData?.indexOf(item) ?? -1;
            return {
              djlxbs: templateStore.djlx,
              gzbh: templateStore.gzbh,
              dj_rec: item.dj_rec || (actualRowIndex > -1 ? actualRowIndex + 1 : 1), // 优先使用行内存储的dj_rec值
            };
          });

          try {
            // 调用删除校验接口
            const checkResponse = await djDeleteCheckApi(checkData);
            const checkResults = checkResponse.data || [];
            // 处理校验结果
            const hasBlockingError = checkResults.some((result) => result.pass === -1);
            const hasWarning = checkResults.some((result) => result.pass === 1);

            if (hasBlockingError) {
              // 存在不允许删除的记录（pass = -1）
              const errorMessages = checkResults
                .filter((result) => result.pass === -1)
                .map((result) => result.title)
                .filter(Boolean);

              // 保存当前活跃元素
              const activeElement = document.activeElement as HTMLElement;
              const modal = Modal.warn({
                title: '删除失败',
                content: errorMessages.join('\n') || '不允许删除',
                onOk: () => {
                  // 延迟一帧后将焦点返回到之前的元素
                  setTimeout(() => {
                    if (activeElement) {
                      activeElement.focus();
                    }
                  }, 0);
                },
              });

              // 添加自定义键盘事件处理
              const handleKeyDown = (e: KeyboardEvent) => {
                if (e.key === 'Enter') {
                  modal.destroy();
                  document.removeEventListener('keydown', handleKeyDown);
                  // 延迟一帧后将焦点返回到之前的元素
                  setTimeout(() => {
                    if (activeElement) {
                      activeElement.focus();
                    }
                  }, 0);
                }
              };
              document.addEventListener('keydown', handleKeyDown);
              return;
            }

            if (hasWarning) {
              // 有需要提示的记录（pass = 1）
              const warningMessages = checkResults
                .filter((result) => result.pass === 1)
                .map((result) => result.title)
                .filter(Boolean);

              Modal.confirm({
                title: '删除确认',
                content: warningMessages.join('\n') || '是否确认删除？',
                okText: '继续',
                cancelText: '返回',
                onOk: async () => {
                  // 用户选择继续，执行删除操作
                  await performDelete(checkedData, null, tableData);
                },
              });
              return;
            }

            // 其他情况（pass = 0 或其他值），直接删除
            await performDelete(checkedData, null, tableData);
          } catch (error) {
            console.error('删除校验失败:', error);
            // 保存当前活跃元素
            const activeElement = document.activeElement as HTMLElement;
            const modal = Modal.warn({
              title: '删除失败',
              content: '删除校验时发生错误',
              onOk: () => {
                // 延迟一帧后将焦点返回到之前的元素
                setTimeout(() => {
                  if (activeElement) {
                    activeElement.focus();
                  }
                }, 0);
              },
            });

            // 添加自定义键盘事件处理
            const handleKeyDown = (e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                modal.destroy();
                document.removeEventListener('keydown', handleKeyDown);
                // 延迟一帧后将焦点返回到之前的元素
                setTimeout(() => {
                  if (activeElement) {
                    activeElement.focus();
                  }
                }, 0);
              }
            };
            document.addEventListener('keydown', handleKeyDown);
          }
          break;
        case 'menuadd':
          // 检查是否已存在空行，如果存在则不新增
          const tableData3 = tableRef.value?.getTableData().fullData;
          const hasEmptyRowInTable3 = tableData3 && tableData3.some((row) => isRowEmpty(row));
          if (!hasEmptyRowInTable3) {
            tableRef.value?.insertAt({}, -1);
            // 更新行编号
            updateRowDjRec();
          }
          break;
        case 'copydata':
          const tableData4 = tableRef.value?.getTableData().fullData;
          const hasEmptyRowInTable4 = tableData4 && tableData4.some((row) => isRowEmpty(row));
          if (!hasEmptyRowInTable4) {
            const rowData = { ...row, _X_ROW_KEY: Math.random() };
            tableRef.value?.insertAt(rowData, -1);
            tableRef.value?.setCheckboxRow(rowData, true);
            // 更新行编号
            updateRowDjRec();
          }
          break;
        case 'pmnrecsel':
          tableRef.value?.setCheckboxRow(row, true);
          break;
        case 'allrecsel':
          tableRef.value?.setAllCheckboxRow(true);
          break;
        case 'norecsel':
          tableRef.value?.setAllCheckboxRow(false);
          tableRef.value?.clearCurrentRow();
          break;
        case 'recfind':
          if (!addedFilter.value) {
            gridOptions.columns = addFilter(gridOptions.columns);
            addedFilter.value = !addedFilter.value;
            return;
          }
          break;
        case 'jefirst':
          handleExpEvent(1);
          break;
        case 'sefirst':
          handleExpEvent(2);
          break;
        case 'djfirst':
          handleExpEvent(3);
          break;
        case 'excelimp_orderinput':
          // EXCEL导入功能
          if (menu.code && menu.code.startsWith('ExcelIMP_')) {
            currentExcelImportMenuCode.value = menu.code;
            excelImportModalOpen.value = true;
            return;
          }
          break;
        case 'getddwinfo':
          // 单位详情查询
          {
            // 从非明细项(XForm)获取ddwid字段的值
            const ddwid = templateStore.templateForm.kbxtable.ddwid;

            if (!ddwid) {
              Modal.warn({
                title: '查询错误',
                content: '未找到单位编号，请先选择往来单位',
                okText: '确定',
              });
              break;
            }

            try {
              // 调用档案资料查询接口
              const response = await queryArchiveDataApi({ ddwid });
              // 保存数据
              ziliaoData.value = response;
              // 打开详情弹窗
              ziliaoModalOpen.value = true;
            } catch (error) {
              console.error('往来单位档案查询失败:', error);
              Modal.warn({
                title: '查询错误',
                content: '往来单位档案查询失败，请稍后重试',
                okText: '确定',
              });
            }
          }
          break;
        case 'getdspinfo':
          // 商品详情查询
          {
            // 获取当前选中行
            const currentRow = tableRef.value?.getCurrentRecord();

            if (!currentRow || !currentRow.dspid) {
              Modal.warn({
                title: '查询错误',
                content: '未找到商品编号，请先选择商品',
                okText: '确定',
              });
              break;
            }

            try {
              // 调用档案资料查询接口
              const response = await queryArchiveDataApi({ dspid: currentRow.dspid });
              // 保存数据
              ziliaoData.value = response;
              // 打开详情弹窗
              ziliaoModalOpen.value = true;
            } catch (error) {
              console.error('商品档案查询失败:', error);
              Modal.warn({
                title: '查询错误',
                content: '商品档案查询失败，请稍后重试',
                okText: '确定',
              });
            }
          }
          break;
        case 'djlxfilecomm':
          // 附件管理功能
          fileManagementModalOpen.value = true;
          break;
        default:
          // 保存当前活跃元素
          const activeElement = document.activeElement as HTMLElement;
          const modal = Modal.success({
            title: '操作提示',
            content: `点击了 ${menu.code}`,
            okText: '确定',
            onOk: () => {
              // 延迟一帧后将焦点返回到之前的元素
              setTimeout(() => {
                if (activeElement) {
                  activeElement.focus();
                }
              }, 0);
            },
          });

          // 添加自定义键盘事件处理
          const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              modal.destroy();
              document.removeEventListener('keydown', handleKeyDown);
              // 延迟一帧后将焦点返回到之前的元素
              setTimeout(() => {
                if (activeElement) {
                  activeElement.focus();
                }
              }, 0);
            }
          };
          document.addEventListener('keydown', handleKeyDown);
          break;
      }
    },
    // 按键事件
    // keydown(params) {
    //   const { $event, $grid, $table, column, row } = params;
    //   if ($event.key === 'Enter') {
    //     console.log('按键事件event', $event);
    //     console.log('按键事件grid', $grid);
    //     console.log('按键事件table', $table);
    //     console.log('params', column, row);
    //   }
    // },

    // 优化后的编辑结束事件处理
    async editClosed({ row, column, rowIndex }) {
      if (row) {
        tableRef.value?.setCheckboxRow(row, false);
      }

      // 更新上一次编辑位置记录
      if (currentEditPosition.value.rowIndex !== null) {
        previousEditPosition.value = { ...currentEditPosition.value };
      }

      // 清空当前编辑位置
      currentEditPosition.value = {
        rowIndex: null,
        fieldName: null,
        row: null,
      };

      // 只有在有效列和不在校验状态时才进行校验
      if (column && column.field && !isValidating.value) {
        // 延迟检查焦点变化情况
        setTimeout(async () => {
          const activeElement = document.activeElement;
          const isTableInput =
            activeElement &&
            activeElement.closest('.vxe-table') &&
            (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA');

          // 获取新焦点的位置信息
          let newRowIndex: number | null = null;
          let newFieldName: string | null = null;
          let isSameRow = false;

          if (isTableInput) {
            // 查找新焦点对应的行和列信息
            const cellElement = activeElement.closest('.vxe-body--column');
            if (cellElement) {
              const fieldAttr = cellElement.getAttribute('data-field');
              if (fieldAttr) {
                newFieldName = fieldAttr;
                // 查找对应的行索引
                const tableData = tableRef.value?.getTableData().fullData;
                if (tableData) {
                  const rowElement = activeElement.closest('.vxe-body--row');
                  if (rowElement) {
                    const rowIndexAttr = rowElement.getAttribute('data-rowindex');
                    if (rowIndexAttr) {
                      newRowIndex = parseInt(rowIndexAttr, 10);
                      isSameRow = newRowIndex === rowIndex;
                    }
                  }
                }
              }
            }
          }

          // 判断校验场景
          if (isTableInput && isSameRow) {
            // 场景1：同行输入框切换 - 需要校验离开的输入框
            await handleCellValidationForSwitch(row, column, rowIndex, newRowIndex, newFieldName);
          } else if (isTableInput && !isSameRow) {
            // 场景2：跨行输入框切换 - 需要校验并处理焦点管理
            await handleCellValidationForCrossRow(row, column, rowIndex, newRowIndex, newFieldName);
          } else {
            // 场景3：完全失焦（点击非输入框区域）- 需要校验并可能清空数据
            await handleCellValidation(row, column, rowIndex);
          }
        }, 50); // 50ms延迟，确保新的焦点已经设置
      }

      // 数据变更后的处理逻辑
      if (column && column.field) {
        // 验证数据
        const valid = validateTableData(row, column);
        if (valid) {
          // 如果验证通过，执行后续逻辑（如计算公式、挂账等）
          const { mxTableList } = templateStore.djInfo || {};
          const fieldConfig = Object.values(mxTableList || {}).find(
            (config: any) => config.fieldName === column.field,
          );

          // 当任何字段值发生变化时，重新计算表达式字段
          const currentTableData = tableRef.value?.getTableData().fullData;
          if (currentTableData && currentTableData.length > 0) {
            const calculatedData = calcMxtable(currentTableData);
            // 更新表格数据但不重新加载（避免焦点丢失）
            currentTableData.forEach((row, index) => {
              if (calculatedData[index]) {
                // 只更新表达式字段的值 - 按 computerOrder 排序以保持一致性
                const formatList = (mxTableList || [])
                  .filter((item) => item.expContent && item.expContent.trim() !== '')
                  .sort((a, b) => {
                    const orderA =
                      (a as any).computerOrder != null
                        ? Number((a as any).computerOrder)
                        : Number.MAX_SAFE_INTEGER;
                    const orderB =
                      (b as any).computerOrder != null
                        ? Number((b as any).computerOrder)
                        : Number.MAX_SAFE_INTEGER;
                    return orderA !== orderB
                      ? orderA - orderB
                      : (a.fieldName || '').localeCompare(b.fieldName || '');
                  });
                formatList.forEach((item) => {
                  if (item.fieldName) {
                    row[item.fieldName] = calculatedData[index][item.fieldName];
                  }
                });
              }
            });

            // 计算汇总项
            calcAllSummaryItems();
          }

          if (fieldConfig && (fieldConfig as any).isSaveNow) {
            // 立即触发挂账
            const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
            await templateStore.saveGzToServer(storageKey);
          }
        }
      }
    },
  };

  const handleExpEvent = (event: any) => {
    const expressions = {
      1: {
        // 金额优先
        jiashhj: 'hsj * shul',
        jine: 'danjia * shul',
        shuie: 'jiashhj - jine',
      },
      2: {
        // 税额优先
        jiashhj: 'hsj * shul',
        shuie: 'jiashhj / (1 + shuil) * shuil',
        jine: 'jiashhj - shuie',
      },
      3: {
        // 单价优先
        jine: 'danjia * shul',
        shuie: 'jine * shuil',
        jiashhj: 'jine + shuie',
      },
    };
    templateStore.setTempExpContent(expressions[event]);
    const tableData = calcMxtable(tableRef.value?.getTableData().fullData);
    const kbxtable = templateStore.templateForm.kbxtable;
    templateStore.setTemplateForm({
      kbxtable,
      mxtable: tableData,
    });
  };

  const handlerToolbar = (item: MenuInfo) => {
    currentFieldName.value = '';
    // 检查是否为商品档案查询或往来单位档案查询
    const functionName = item.functionName || item.lcTitle;
    if (functionName === 'getdspinfo' || functionName === 'getddwinfo') {
      handleEvent({ functionName }, '2');
      return;
    }

    handleEvent({ functionName }, '2');
  };

  interface DropMenu {
    onClick?: Fn;
    to?: string;
    icon?: string;
    event: string | number;
    text: string;
    disabled?: boolean;
    divider?: boolean;
    sonMenuList?: DropMenu[];
  }

  function handleMenuEvent(menu: DropMenu) {
    currentFieldName.value = '';
    console.log('menu', menu);
    // 检查是否为商品档案查询或往来单位档案查询
    const functionName = String(menu.event || menu.text);

    // 将菜单的text值赋值给XFrom的goudfs字段
    if (menu.text && functionName.startsWith('get_goudui_')) {
      // 获取当前挂账编号
      const currentGzbh = templateStore.gzbh;

      // 检查是否已经使用了某种勾兑方式
      if (currentGzbh && gouduiMethodMap.value.has(currentGzbh)) {
        const recordedMethod = gouduiMethodMap.value.get(currentGzbh);
        // 如果已经记录了勾兑方式，且与当前选择的不同，显示错误提示并阻止切换
        if (recordedMethod && recordedMethod !== functionName) {
          Modal.warn({
            title: '勾兑方式冲突',
            content: `当前单据已使用"${recordedMethod.replace('get_goudui_', '')}"方式勾兑，不能切换为"${functionName.replace('get_goudui_', '')}"方式`,
            okText: '确定',
          });
          return; // 不允许切换勾兑方式
        }
      }

      // 如果没有使用过勾兑方式或使用的是相同的勾兑方式，则允许设置
      templateStore.templateForm.kbxtable.goudfs = menu.text;
    }

    if (functionName === 'getdspinfo' || functionName === 'getddwinfo') {
      handleEvent({ functionName }, '2');
      return;
    }

    handleEvent({ functionName }, '2');
  }
  selectedKeys.value = [''];

  const gridOptions = reactive({
    id: 'InvoiceTable',
    columns: [] as any[],
    editConfig: {
      trigger: 'click',
      mode: 'cell',
      showStatus: true,
    },
    padding: 0,
    filterConfig: {
      // 启用筛选
      showIcon: false, // 显示筛选图标
      remote: false, // 使用本地过滤
      defaultFilterMethod: ({ option, row, column }) => {
        if (option.data) {
          const cellValue = row[column.field] ?? '';
          return String(cellValue).toLowerCase().includes(String(option.data).toLowerCase());
        }
        return true;
      },
    },
    showOverflow: true,
    columnConfig: {
      resizable: true,
      // 添加拖拽支持
      isCurrent: true,
      isHover: true,
      dragSort: true,
    },
    resizableConfig: {
      minWidth: 0,
      maxWidth: 10000,
      resizeEnd: ({ column, resizeWidth }) => {
        console.log('列宽变化:', {
          field: column.field,
          title: column.title,
          oldWidth: column.width,
          newWidth: resizeWidth,
        });
      },
    },
    customConfig: {
      storage: true,
      mode: 'popup',
      // 添加对拖拽的支持
      checkMethod: () => true, // 所有列都可以被选中/取消选中
      dragSort: true, // 启用拖拽排序
      sortMethod: () => true, // 允许所有排序操作
      showIcon: true, // 显示图标
      updateStore({ storeData }) {
        console.log('保存列宽和顺序数据:', storeData);

        // 检查数据完整性
        if (!storeData || !storeData.sortData) {
          console.warn('列设置数据不完整，无法保存');
          return Promise.resolve({ code: '0000', message: '数据不完整，跳过保存' });
        }

        // 确保数据格式正确，再调用API
        return updateQueryColWidthApi({
          pagetype: templateStore.djlx,
          flds: transformColumnData(storeData),
        });
      },
      restoreStore: async ({ id: _id }) => {
        // 从服务器恢复列设置
        // 暂时留空，需要时可以实现
        return '';
      },
      slots: {
        footer: 'customFooter',
      },
    },
    toolbarConfig: {
      custom: false,
      export: false,
      print: false,
      zoom: false,
      refresh: false,
      slots: {
        buttons: 'toolbar_buttons',
      },
    },
    formConfig: {
      enabled: true,
      items: [],
      events: formEvents,
    },
    footerConfig: {
      enabled: true,
      items: [],
    },
    keyboardConfig: {
      isArrow: true,
      isTab: true,
      isDel: true,
      isSelect: true,
      isEnter: true,
    },
    border: true,
    size: 'mini',
    height: 'auto',
    proxyConfig: {
      ajax: {
        query: async ({ page: _page, form: _form }) => {
          const { mxtable } = templateStore.templateForm;
          if (mxtable && mxtable.length > 0) {
            // 确保至少有一个空行
            nextTick(() => {
              ensureAtLeastOneEmptyRow();
            });
            return {
              items: mxtable,
              total: mxtable.length,
            };
          }
          // 如果没有数据，返回一个空行
          const emptyRow = {};
          if (djInfo.value?.mxTableList) {
            djInfo.value.mxTableList.forEach((item) => {
              if (item.fldType === '1' || item.fldType === 1) {
                emptyRow[item.field] = null;
              } else if (item.fldType === '2' || item.fldType === 2) {
                emptyRow[item.field] = null;
              } else {
                emptyRow[item.field] = null;
              }
            });
          }
          return {
            items: [emptyRow],
            total: 1,
          };
        },
      },
    },
    menuConfig: {
      body: {
        options: [[]],
      },
    },
    stripe: true,
    headerConfig: {
      height: 40,
    },
    showHeaderOverflow: 'title',
  });

  onActivated(async () => {
    if (tableRef.value) {
      tableRef.value.refreshColumn();
    }
    sonMxGridOptions.value = null;
    // if (templateStore.resetForm) {
    // xFormRef.value.resetForm();
    // templateStore.setResetForm(false);
    // }
    if (firstLoad.value) return;
    djlx.value = query.djlx as string;
    if (!djlx.value) {
      // 如果djlx为空，直接跳转到首页
      closeCurrent();
      router.push('/home/<USER>');

      return;
    }

    // 如果当前url中的djlx与store中的djlx不一致，需要重新初始化
    if (!templateStore.gzbh || templateStore.djlx !== djlx.value) {
      // 如果djlx发生变化，先清理旧的状态数据
      if (templateStore.djlx && templateStore.djlx !== djlx.value) {
        console.log(`djlx从 ${templateStore.djlx} 切换到 ${djlx.value}，清理旧状态`);
        // 重置表单
        xFormRef.value?.resetForm();
      }
      if (djlx.value) {
        await templateStore.initGz({
          djlx: djlx.value + '',
          djbs: djbs.value,
          kbxTableData: {},
          mxTableDataList: [],
        });
        const response = await templateDetailApi(djlx.value);
        templateStore.setDjInfo(response);
        // const response = data;
        // 设置tab title
        setTitle(response.runTitle);
        djInfo.value = jsonConvert(response);
        tableHeight.value = computedTableHeight();
        // 修改表单配置
        gridOptions.formConfig.items = djInfo.value.kbxTableList
          .filter((item) => item.upMx && item.isActive)
          .sort((a, b) => a.fieldTaborder - b.fieldTaborder);
        gridOptions.footerConfig.items = djInfo.value.kbxTableList
          .filter((item) => !item.upMx && item.isActive)
          .sort((a, b) => a.fieldTaborder - b.fieldTaborder);

        gridOptions.columns = [
          { type: 'checkbox', width: 60, fixed: 'left' },
          { type: 'seq', title: '序号', width: 60, fixed: 'left' },
        ].concat(djInfo.value.mxTableList.filter((item) => item.isActive));
        gridOptions.columns = addEvents(gridOptions.columns);
        gridOptions.columns = addDisable(gridOptions.columns);
        let options: { code: string; name: any; visible: boolean; disabled: boolean }[][] = [];
        const menuList: { code: string; name: any; visible: boolean; disabled: boolean }[] = [];
        response.menuInfoList
          ?.filter((i) => i.menuLx == 'P' && i.isActive)
          .forEach((menuItem) => {
            if (menuItem.lcTitle === '-') return;
            const menu: {
              code: string;
              name: any;
              visible: boolean;
              disabled: boolean;
              children: { code: string; name: any; visible: boolean; disabled: boolean }[];
            } = {
              code: menuItem.functionName || menuItem.lcTitle,
              name: menuItem.lcTitle,
              visible: true,
              disabled: false,
              children: [],
            };
            menuList.push(menu);
            // 判断menuItem是否有子菜单
            if (menuItem.sonMenuList && menuItem.sonMenuList.length > 0) {
              menuItem.sonMenuList.forEach((sonMenuItem) => {
                const sonMenu = {
                  code: sonMenuItem.functionName || sonMenuItem.lcTitle,
                  name: sonMenuItem.lcTitle,
                  visible: true,
                  disabled: false,
                };
                menu.children.push(sonMenu);
              });
            }
          });
        options.push(menuList);
        const menuBody = {
          body: {
            options,
          },
        };
        gridOptions.menuConfig = menuBody;
        djbs.value = response.djbs;
        let mxTableDataList: any = [];
        if (response.mxTableList) {
          const mxTableData = {};
          djInfo.value.mxTableList?.forEach((item: any) => {
            mxTableData[item.field] = null;
          });
          mxTableDataList.push(mxTableData);
        } else {
          mxTableDataList = undefined;
        }
        const kbxTableData = {};

        djInfo.value.kbxTableList
          .filter(
            (i) => i.itemRender.name !== 'AButton' && i.itemRender.name !== 'AText' && i.isActive,
          )
          .map((item: any) => {
            kbxTableData[item.fieldName] = null;
          });
        // 初始化子明细
        if (response.mxTableList) {
          response.mxTableList.forEach((item) => {
            if (item.existSonMx) {
              sonMxTable.value = item;
              return;
            }
          });
        }

        await templateStore.setTemplateForm({
          kbxTableData,
          mxTableDataList,
        });
        templateStore.setDjbs(djbs.value);
        tableRef.value?.commitProxy('query');
        templateStore.setFuncInfo(response.funcinfo);
        // 确保表格至少有一个空行
        nextTick(() => {
          ensureAtLeastOneEmptyRow();
        });
      } else {
        console.error('Invalid query parameter: djlx');
      }
      const gzbh = Array.isArray(query.gzbh) ? query.gzbh[0] : query.gzbh;
      const bizId = Array.isArray(query.bizId) ? query.bizId[0] : query.bizId;
      const storageKey = `gz_${djlx.value}`;
      if (gzbh && bizId) {
        // 场景1：当gzbh存在时，通过gzbh查询
        await templateStore.fetchTemplateForm(gzbh, bizId);
        // 等待fetchTemplateForm完成后，保存规则到服务器
        await templateStore.saveGzToServer(storageKey);
      } else if (templateStore.djlx && templateStore.djbs) {
        // 场景2：用户重新登录系统，查询是否存在历史挂账
        await templateStore.fetchTemplateForm(
          undefined,
          undefined,
          templateStore.djlx,
          templateStore.djbs,
        );
        // 等待fetchTemplateForm完成后，保存规则到服务器
        await templateStore.saveGzToServer(storageKey);
      } else {
        // 如果没有历史数据需要查询，直接保存初始状态
        await templateStore.saveGzToServer(storageKey);
      }
    }
  });

  // onDeactivated(() => {
  // Modal.confirm({
  //   title: '挂账提示',
  //   content: '是否挂账？',
  //   onOk: async () => {
  //     templateStore.setResetForm(true);
  //     templateStore.setGzbh(''); // 页面跳转后，清除当前gzbh
  //   },
  //   onCancel: async () => {
  //     templateStore.clearGz([templateStore.gzbh]);
  //     templateStore.setResetForm(true);
  //     templateStore.setAllNull();
  //     templateStore.setGzbh('');
  //   },
  // });
  // });
  // 根据query查询数据
  onMounted(async () => {
    window.addEventListener('resize', handleResize);
    sonMxGridOptions.value = null;

    xFormRef.value.resetForm();
    templateStore.setResetForm(false);

    djlx.value = query.djlx as string;
    if (!djlx.value) {
      // 如果djlx为空，直接跳转到首页
      closeCurrent();
      router.push('/home/<USER>');
      return;
    }

    if (djlx.value) {
      await templateStore.initGz({
        djlx: djlx.value + '',
        djbs: djbs.value,
        kbxTableData: {},
        mxTableDataList: [],
      });
      const response = await templateDetailApi(djlx.value);
      templateStore.setDjInfo(response);
      // const response = data;
      // 设置tab title
      setTitle(response.runTitle);
      djInfo.value = jsonConvert(response);
      console.log('djInfo.value', djInfo.value);
      tableHeight.value = computedTableHeight();
      // 修改表单配置
      gridOptions.formConfig.items = djInfo.value.kbxTableList
        .filter((item) => item.upMx && item.isActive)
        .sort((a, b) => a.fieldTaborder - b.fieldTaborder);
      gridOptions.footerConfig.items = djInfo.value.kbxTableList
        .filter((item) => !item.upMx && item.isActive)
        .sort((a, b) => a.fieldTaborder - b.fieldTaborder);
      // gridOptions.height = response.mxHeight;
      gridOptions.columns = [
        { type: 'checkbox', width: 60, fixed: 'left' },
        { type: 'seq', title: '序号', width: 60, fixed: 'left' },
      ].concat(djInfo.value.mxTableList.filter((item) => item.isActive));
      gridOptions.columns = addEvents(gridOptions.columns);
      gridOptions.columns = addDisable(gridOptions.columns);
      let options: { code: string; name: any; visible: boolean; disabled: boolean }[][] = [];
      const menuList: { code: string; name: any; visible: boolean; disabled: boolean }[] = [];
      response.menuInfoList
        ?.filter((i) => i.menuLx == 'P' && i.isActive)
        .forEach((menuItem) => {
          if (menuItem.lcTitle === '-') return;
          const menu: {
            code: string;
            name: any;
            visible: boolean;
            disabled: boolean;
            children: { code: string; name: any; visible: boolean; disabled: boolean }[];
          } = {
            code: menuItem.functionName || menuItem.lcTitle,
            name: menuItem.lcTitle,
            visible: true,
            disabled: false,
            children: [],
          };
          menuList.push(menu);
          // 判断menuItem是否有子菜单
          if (menuItem.sonMenuList && menuItem.sonMenuList.length > 0) {
            menuItem.sonMenuList.forEach((sonMenuItem) => {
              const sonMenu = {
                code: sonMenuItem.functionName || sonMenuItem.lcTitle,
                name: sonMenuItem.lcTitle,
                visible: true,
                disabled: false,
              };
              menu.children.push(sonMenu);
            });
          }
        });
      options.push(menuList);
      const menuBody = {
        body: {
          options,
        },
      };
      gridOptions.menuConfig = menuBody;
      djbs.value = response.djbs;
      let mxTableDataList: any = [];
      if (response.mxTableList) {
        const mxTableData = {};
        djInfo.value.mxTableList?.forEach((item: any) => {
          mxTableData[item.field] = null;
        });
        mxTableDataList.push(mxTableData);
      } else {
        mxTableDataList = undefined;
      }
      const kbxTableData = {};
      djInfo.value.kbxTableList
        .filter(
          (i) => i.itemRender.name !== 'AButton' && i.itemRender.name !== 'AText' && i.isActive,
        )
        .map((item: any) => {
          kbxTableData[item.fieldName] = null;
        });
      if (response.mxTableList) {
        response.mxTableList.forEach((item) => {
          if (item.existSonMx) {
            sonMxTable.value = item;
            return;
          }
        });
      }
      await templateStore.setTemplateForm({
        kbxTableData,
        mxTableDataList,
      });
      templateStore.setDjbs(djbs.value);
      tableRef.value?.commitProxy('query');
      templateStore.setFuncInfo(response.funcinfo);
      firstLoad.value = false;
      // 默认选中kbxTable第一个输入框

      nextTick(() => {
        if (xFormRef.value) {
          xFormRef.value.jumpToFirstInput();
        } else {
          console.warn('xFormRef 未正确初始化');
        }
        // 确保表格至少有一个空行
        ensureAtLeastOneEmptyRow();
      });
      const gzbh = Array.isArray(query.gzbh) ? query.gzbh[0] : query.gzbh;
      const bizId = Array.isArray(query.bizId) ? query.bizId[0] : query.bizId;
      const storageKey = `gz_${djlx.value}`;
      if (gzbh && bizId) {
        // 场景1：当gzbh存在时，通过gzbh查询
        await templateStore.fetchTemplateForm(gzbh, bizId);
        tableRef.value?.commitProxy('query');
        // 等待fetchTemplateForm完成后，保存规则到服务器
        await templateStore.saveGzToServer(storageKey);
        // 确保表格至少有一个空行
        nextTick(() => {
          ensureAtLeastOneEmptyRow();
        });
      } else if (templateStore.djlx && templateStore.djbs) {
        // 场景2：用户重新登录系统，查询是否存在历史挂账
        await templateStore.fetchTemplateForm(
          undefined,
          undefined,
          templateStore.djlx,
          templateStore.djbs,
        );
        tableRef.value?.commitProxy('query');
        // 等待fetchTemplateForm完成后，保存规则到服务器
        await templateStore.saveGzToServer(storageKey);
      } else {
        // 如果没有历史数据需要查询，直接保存初始状态
        await templateStore.saveGzToServer(storageKey);
      }
    } else {
      console.error('Invalid query parameter: dj00lx');
    }
  });

  async function initSonMxTable(row?: any) {
    if (sonMxTable.value && row[sonMxTable.value.fieldName]) {
      const response = await getSonMxDetailApi({
        djlx: templateStore.djlx,
        paramname: sonMxTable.value.fieldName,
        paramvalue: row ? row[sonMxTable.value.fieldName] : '',
      });
      const columns: Array<{ title: string; field: string; key: string; width: number }> = [];
      if (response.tableheader) {
        // 设置表头
        Object.entries(response.tableheader).forEach(([key, value]) => {
          columns.push({
            title: String(value),
            field: key,
            key: key,
            width: Math.max(
              String(value).length * 20, // 表头文字宽度
              key.length * 15, // 字段名宽度
              100, // 最小宽度
            ),
            showOverflow: true,
            showHeaderOverflow: true,
          });
        });
      }

      sonMxGridOptions.value = {
        id: 'SonMxTable',
        columns,
        border: true,
        size: 'mini',
        padding: false,
        height: 'auto',
        showOverflow: true, // 添加此配置，确保内容溢出时显示省略号
        showHeaderOverflow: 'title', // 添加此配置，确保表头溢出时显示省略号
        columnConfig: {
          resizable: true, // 允许调整列宽
        },
        toolbarConfig: {
          custom: false,
          export: false,
          print: false,
          zoom: false,
          refresh: false,
          slots: {
            buttons: 'toolbar_buttons',
          },
        },
        headerConfig: {
          height: 40, // 与父表格保持一致的表头高度
        },
        data: response.tablevalue,
        proxyConfig: {
          ajax: {
            query: async () => {
              if (response.tablevalue) {
                return {
                  items: response.tablevalue,
                  total: response.tablevalue.length,
                };
              }
              return {
                items: [],
                total: 0,
              };
            },
          },
        },
      };
    } else {
      sonMxGridOptions.value = null;
    }
    sonTableRef.value?.commitProxy('query');
  }

  // 处理窗口大小变化
  const handleResize = debounce(() => {
    if (tableRef.value) {
      tableHeight.value = computedTableHeight();
      tableRef.value.refreshColumn();
    }
    if (sonTableRef.value) {
      sonTableRef.value.refreshColumn();
    }
  }, 100);

  // 在 onMounted 中添加监听
  onMounted(() => {
    window.addEventListener('resize', handleResize);
    // 添加键盘事件监听
    if (mainTableContainer.value) {
      mainTableContainer.value.addEventListener('keydown', handleTableKeydown);
    }
  });

  // 在 onUnmounted 中移除监听
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    // 移除键盘事件监听
    if (mainTableContainer.value) {
      mainTableContainer.value.removeEventListener('keydown', handleTableKeydown);
    }
  });

  function handleCloseCurrent() {
    Modal.confirm({
      title: '提示',
      content: '是否退出？',
      onOk: async () => {
        const existingGzbh = templateStore.gzbh;
        const bizId = query.bizId;
        try {
          // 清除服务器挂账
          if (existingGzbh) {
            await templateStore.clearGz([existingGzbh], bizId);
            // 清除本地缓存
            const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
            const existingDataStr = localStorage.getItem('gz_data') || '{}';
            const existingData = JSON.parse(existingDataStr);
            if (existingData[storageKey]) {
              delete existingData[storageKey];
              localStorage.setItem('gz_data', JSON.stringify(existingData));
            }
          }
          closeCurrent();
          templateStore.setGzbh('');
          templateStore.setResetForm(true);
        } catch (error) {
          console.error('退出时清除挂账失败:', error);
          Modal.warn({
            title: '操作失败',
            content: '清除挂账时发生错误，请稍后重试。',
            okText: '确定',
          });
          // 即使清除失败，也尝试关闭
          closeCurrent();
          templateStore.setGzbh('');
          templateStore.setResetForm(true);
        }
      },
      onCancel: () => {
        // 用户取消退出，无需操作
      },
    });
  }

  function quit() {
    clearGzModalOpen.value = false;
    // 清空勾兑方式记录
    gouduiMethodMap.value.clear();
    // 持久化清除操作
    saveGouduiMethodsToStorage();
    closeCurrent();
  }

  function saveForm() {
    debouncedSave();
  }

  // 创建防抖的保存方法
  const debouncedSave = debounce(() => {
    if (templateStore.gzbh) {
      // 保存时会在 templateStore 中检查空值情况
      templateStore.saveGz();
    }
  }, 1000);

  async function handleEvent(event: any, eventType?: string) {
    // 识别是否是商品档案查询函数
    if (event.functionName === 'getdspinfo') {
      // 获取当前选中行
      const currentRow = tableRef.value?.getCurrentRecord();

      if (!currentRow || !currentRow.dspid) {
        Modal.warn({
          title: '查询错误',
          content: '未找到商品编号，请先选择商品',
          okText: '确定',
        });
        return;
      }

      try {
        // 调用档案资料查询接口
        const response = await queryArchiveDataApi({ dspid: currentRow.dspid });
        // 保存数据
        ziliaoData.value = response;
        // 打开详情弹窗
        ziliaoModalOpen.value = true;
      } catch (error) {
        console.error('商品档案查询失败:', error);
        Modal.warn({
          title: '查询错误',
          content: '商品档案查询失败，请稍后重试',
          okText: '确定',
        });
      }
      return;
    }

    // 识别是否是往来单位档案查询函数
    if (event.functionName === 'getddwinfo') {
      // 从非明细项(XForm)获取ddwid字段的值
      const ddwid = templateStore.templateForm.kbxtable.ddwid;

      if (!ddwid) {
        Modal.warn({
          title: '查询错误',
          content: '未找到单位编号，请先选择往来单位',
          okText: '确定',
        });
        return;
      }

      try {
        // 调用档案资料查询接口
        const response = await queryArchiveDataApi({ ddwid });
        // 保存数据
        ziliaoData.value = response;
        // 打开详情弹窗
        ziliaoModalOpen.value = true;
      } catch (error) {
        console.error('往来单位档案查询失败:', error);
        Modal.warn({
          title: '查询错误',
          content: '往来单位档案查询失败，请稍后重试',
          okText: '确定',
        });
      }
      return;
    }
    if (event.functionName.startsWith('ExcelIMP_')) {
      currentExcelImportMenuCode.value = event.functionName;
      excelImportModalOpen.value = true;
      return;
    }
    switch (event.functionName) {
      case 'djgzxz':
        // 弹出备注输入框
        {
          let remarkValue = '';
          Modal.confirm({
            title: '单据挂账',
            content: h('div', [
              h('p', { style: 'margin-bottom: 8px;' }, '请输入备注信息：'),
              h('textarea', {
                placeholder: '请输入备注信息（必填）',
                style:
                  'width: 100%; height: 80px; resize: vertical; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;',
                onInput: (e: Event) => {
                  remarkValue = (e.target as HTMLTextAreaElement).value;
                },
              }),
            ]),
            okText: '确定挂账',
            cancelText: '取消',
            keyboard: true,
            onOk: async () => {
              try {
                // 使用闭包变量获取备注内容
                const beizhu = remarkValue;

                // 验证备注信息是否为空
                if (!beizhu || beizhu.trim() === '') {
                  Modal.warn({
                    title: '提示',
                    content: '请输入备注信息，备注信息不能为空',
                    okText: '确定',
                  });
                  return;
                }

                // 挂账前强制重新计算所有表达式字段，确保数据是最新的
                console.log('挂账前重新计算表达式字段...');
                forceRecalculateAllExpressions();

                // 验证汇总项计算结果
                // if (!validateSummaryItems()) {
                //   Modal.warn({
                //     title: '汇总项验证失败',
                //     content: '汇总项计算结果存在错误，请检查数据后重试',
                //     okText: '确定',
                //   });
                //   return;
                // }

                const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
                const result = await templateStore.saveGzToServer(storageKey, true, beizhu);
                if (result.success) {
                  const modal = Modal.success({
                    title: '挂账成功',
                    content: `单据已挂账，挂账编号为：${templateStore.gzbh}`,
                    okText: '确定',
                    keyboard: true,
                    onOk: () => {
                      // 挂账成功后清空页面数据，但不调用后端清空接口
                      clearPageDataOnly();
                      document.removeEventListener('keydown', handleEnterKey);
                    },
                  });

                  // 添加回车键监听
                  const handleEnterKey = (e: KeyboardEvent) => {
                    if (e.key === 'Enter') {
                      modal.destroy();
                      // 挂账成功后清空页面数据，但不调用后端清空接口
                      clearPageDataOnly();
                      document.removeEventListener('keydown', handleEnterKey);
                    }
                  };
                  document.addEventListener('keydown', handleEnterKey);
                }
              } catch (error) {
                console.error('挂账失败:', error);
              }
            },
          });
        }
        break;
      case 'djtmpdelete':
        qlDjGzModalOpen.value = true;
        break;
      case 'getgzdj':
        djgzModalOpen.value = true;
        break;
      case 'djprint':
        console.log('单据打印');
        break;
      case 'savedj':
        {
          // 存盘前强制重新计算所有表达式字段，确保数据是最新的
          console.log('存盘前重新计算表达式字段...');
          forceRecalculateAllExpressions();

          // 检查表格数据是否为空
          const tableData = tableRef.value?.getTableData().fullData;
          if (templateStore.djInfo && templateStore.djInfo.mxTableList) {
            const hasValidData =
              tableData &&
              tableData.length > 0 &&
              tableData.some((row) => {
                // 检查行是否有任何有效数据(非空行)，排除内部字段如_X_ROW_KEY和dj_rec
                return Object.entries(row).some(
                  ([key, value]) =>
                    // 排除内部字段：_X_ROW_KEY 是表格内部标识，dj_rec 是系统自动生成的行号
                    key !== '_X_ROW_KEY' &&
                    key !== 'dj_rec' &&
                    value !== null &&
                    value !== undefined &&
                    value !== '',
                );
              });

            if (!hasValidData) {
              const modal = Modal.warn({
                title: '错误',
                content: '单据不存在明细行数据，不可进行存盘操作！',
                okText: '确定',
                keyboard: true, // 支持键盘esc关闭
                onOk: () => {
                  modal.destroy();
                },
              });
              // 添加回车键关闭事件
              const handleEnter = (e) => {
                if (e.key === 'Enter') {
                  modal.destroy();
                  window.removeEventListener('keydown', handleEnter);
                }
              };
              window.addEventListener('keydown', handleEnter);
              break;
            }

            // 去除空白行，包括只有expContent计算值的行
            const { mxTableList } = templateStore.djInfo;
            const expContentFields = (mxTableList || [])
              .filter((item) => item.expContent)
              .map((item) => item.fieldName);

            const filteredData = tableData.filter((row) => {
              // 检查行是否有任何有效数据(非空行)
              return Object.entries(row).some(([key, value]) => {
                // 排除内部字段：_X_ROW_KEY 是表格内部标识，dj_rec 是系统自动生成的行号
                if (key === '_X_ROW_KEY' || key === 'dj_rec') return false;
                // 排除空值
                if (value === null || value === undefined || value === '') return false;
                // 排除只有expContent计算出来的字段
                if (expContentFields.includes(key)) return false;
                return true;
              });
            });

            // 更新表格数据
            if (filteredData.length !== tableData.length) {
              // 如果过滤后没有数据，保留一个空行
              const dataToLoad = filteredData.length > 0 ? filteredData : [{}];
              tableRef.value?.loadData(dataToLoad);
            }

            // 校验明细项中的dkfid和dspid字段
            const currentTableData = tableRef.value?.getTableData().fullData || [];

            // 检查是否存在dkfid和dspid字段
            const hasDkfidField = mxTableList?.some((item) => item.fieldName === 'dkfid');
            const hasDspidField = mxTableList?.some((item) => item.fieldName === 'dspid');
            // 检查是否存在zengp字段
            const hasZengpField = mxTableList?.some((item) => item.fieldName === 'zengp');

            // 检查有效数据行
            const validDataRows = currentTableData.filter((row) => {
              return Object.entries(row).some(([key, value]) => {
                // 排除内部字段：_X_ROW_KEY 是表格内部标识，dj_rec 是系统自动生成的行号
                if (key === '_X_ROW_KEY' || key === 'dj_rec') return false;
                if (value === null || value === undefined || value === '') return false;
                if (expContentFields.includes(key)) return false;
                return true;
              });
            });

            // 如果存在有效数据行，进行字段校验
            if (validDataRows.length > 0) {
              // 校验dkfid字段
              if (hasDkfidField) {
                const hasEmptyDkfid = validDataRows.some(
                  (row) =>
                    !row.dkfid || row.dkfid === '' || row.dkfid === null || row.dkfid === undefined,
                );
                if (hasEmptyDkfid) {
                  const modal = Modal.warn({
                    title: '校验失败',
                    content: '请选择库房',
                    okText: '确定',
                    keyboard: true,
                    onOk: () => {
                      modal.destroy();
                    },
                  });
                  const handleEnter = (e) => {
                    if (e.key === 'Enter') {
                      modal.destroy();
                      window.removeEventListener('keydown', handleEnter);
                    }
                  };
                  window.addEventListener('keydown', handleEnter);
                  break;
                }
              }

              // 校验dspid字段
              if (hasDspidField) {
                const hasEmptyDspid = validDataRows.some(
                  (row) =>
                    !row.dspid || row.dspid === '' || row.dspid === null || row.dspid === undefined,
                );
                if (hasEmptyDspid) {
                  const modal = Modal.warn({
                    title: '校验失败',
                    content: '请选择商品',
                    okText: '确定',
                    keyboard: true,
                    onOk: () => {
                      modal.destroy();
                    },
                  });
                  const handleEnter = (e) => {
                    if (e.key === 'Enter') {
                      modal.destroy();
                      window.removeEventListener('keydown', handleEnter);
                    }
                  };
                  window.addEventListener('keydown', handleEnter);
                  break;
                }
              }
              // 根据后端返回的checkHsjAndShul字段判断是否需要校验hsj和shul字段
              if (djInfo.value?.checkHsjAndShul && validDataRows.length > 0) {
                const targetFields = ['hsj', 'shul'];
                const hasTargetFields = targetFields.some((fieldName) =>
                  mxTableList?.some((item) => item.fieldName === fieldName),
                );

                if (hasTargetFields) {
                  const targetFieldsInConfig = targetFields.filter((fieldName) =>
                    mxTableList?.some((item) => item.fieldName === fieldName),
                  );

                  let shouldBreak = false;
                  for (const row of validDataRows) {
                    for (const fieldName of targetFieldsInConfig) {
                      const fieldValue = row[fieldName];
                      // 检查hsj和shul字段值是否为0
                      if (fieldValue === 0 || fieldValue === '0') {
                        // 检查是否有zengp字段
                        if (!hasZengpField) {
                          const modal = Modal.warn({
                            title: '校验失败',
                            content: '价格不允许为0',
                            okText: '确定',
                            keyboard: true,
                            onOk: () => {
                              modal.destroy();
                            },
                          });
                          const handleEnter = (e) => {
                            if (e.key === 'Enter') {
                              modal.destroy();
                              window.removeEventListener('keydown', handleEnter);
                            }
                          };
                          window.addEventListener('keydown', handleEnter);
                          shouldBreak = true;
                          break; // 跳出内层循环
                        } else {
                          // 有zengp字段，判断zengp是否等于"是"
                          if (!row.zengp || row.zengp !== '是') {
                            const modal = Modal.warn({
                              title: '校验失败',
                              content: '价格不允许为0',
                              okText: '确定',
                              keyboard: true,
                              onOk: () => {
                                modal.destroy();
                              },
                            });
                            const handleEnter = (e) => {
                              if (e.key === 'Enter') {
                                modal.destroy();
                                window.removeEventListener('keydown', handleEnter);
                              }
                            };
                            window.addEventListener('keydown', handleEnter);
                            shouldBreak = true;
                            break; // 跳出内层循环
                          }
                        }
                      }
                    }
                    if (shouldBreak) break; // 跳出外层循环
                  }
                  if (shouldBreak) break; // 中断整个case执行
                }
              }
            }
          }

          // 先调用saveGzToServer保存挂账数据
          const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;

          // 在保存前检查 get_goudui_bugdjs 函数的字段是否有值
          const currentGzbh = templateStore.gzbh;
          if (currentGzbh && gouduiMethodMap.value.has(currentGzbh)) {
            const recordedMethod = gouduiMethodMap.value.get(currentGzbh);
            if (recordedMethod === 'get_goudui_bugdjs') {
              console.log('【勾兑】检测到使用了 bugdjs 勾兑方式，开始验证明细数据');

              // 获取表格数据
              const tableData = tableRef.value?.getTableData().fullData || [];

              // 检查是否存在有效的明细行数据
              let hasValidData = false;

              for (const row of tableData) {
                // 检查行是否有任何有效数据(非空行)
                const hasRowData = Object.entries(row).some(([key, value]) => {
                  // 排除内部字段和系统字段
                  if (key === '_X_ROW_KEY' || key === 'dj_rec') return false;
                  // 排除空值
                  if (value === null || value === undefined || value === '') return false;
                  return true;
                });

                if (hasRowData) {
                  hasValidData = true;
                  break;
                }
              }

              if (!hasValidData) {
                console.log('【勾兑】bugdjs 勾兑方式验证失败：明细数据为空');
                Modal.warn({
                  title: '验证失败',
                  content: '明细不能为空',
                  okText: '确定',
                });
                break; // 阻止保存操作
              }

              console.log('【勾兑】bugdjs 勾兑方式验证通过');
            }
          }

          // 验证汇总项计算结果
          // if (!validateSummaryItems()) {
          //   Modal.warn({
          //     title: '汇总项验证失败',
          //     content: '汇总项计算结果存在错误，请检查数据后重试',
          //     okText: '确定',
          //   });
          //   break;
          // }

          const result = await templateStore.saveGzToServer(storageKey);
          if (result.success) {
            // 调用单据明细校验接口
            const checkResult = await templateStore.checkDjMx();
            const { pass, tsmc, tsinfo } = checkResult.data || checkResult;

            if (pass === -1) {
              // 拦截并提示，用户不可选择
              const modal = Modal.warn({
                title: tsmc || '校验失败',
                content: tsinfo || '单据明细校验不通过，无法继续存盘操作',
                okText: '确定',
                keyboard: true,
                onOk: () => {
                  modal.destroy();
                },
              });
              const handleEnter = (e) => {
                if (e.key === 'Enter') {
                  modal.destroy();
                  window.removeEventListener('keydown', handleEnter);
                }
              };
              window.addEventListener('keydown', handleEnter);
              break;
            } else if (pass === 1) {
              // 拦截并提示，用户可以选择通过或不通过
              const shouldContinue = await new Promise((resolve) => {
                const modal = Modal.confirm({
                  title: tsmc || '校验警告',
                  content: tsinfo || '单据明细校验有警告，是否继续存盘操作？',
                  okText: '继续存盘',
                  cancelText: '取消',
                  keyboard: true,
                  onOk: () => {
                    modal.destroy();
                    resolve(true);
                  },
                  onCancel: () => {
                    modal.destroy();
                    resolve(false);
                  },
                });
                const handleEnter = (e) => {
                  if (e.key === 'Enter') {
                    modal.destroy();
                    window.removeEventListener('keydown', handleEnter);
                    resolve(true);
                  }
                };
                window.addEventListener('keydown', handleEnter);
              });

              if (!shouldContinue) {
                break; // 用户选择取消，直接中断case执行
              }
            }

            // 存盘前再次删除空白行，确保不会将空白行保存到服务器
            const finalTableData = tableRef.value?.getTableData().fullData || [];

            // 重新获取mxTableList和计算字段列表
            const { mxTableList: finalMxTableList } = templateStore.djInfo || {};
            const finalExpContentFields = (finalMxTableList || [])
              .filter((item) => item.expContent)
              .map((item) => item.fieldName);

            const finalFilteredData = finalTableData.filter((row) => {
              // 检查行是否有任何有效数据(非空行)
              return Object.entries(row).some(([key, value]) => {
                // 排除内部字段和系统字段
                if (key === '_X_ROW_KEY' || key === 'dj_rec') return false;
                // 排除空值
                if (value === null || value === undefined || value === '') return false;
                // 排除只有expContent计算出来的字段
                if (finalExpContentFields.includes(key)) return false;
                return true;
              });
            });

            // 更新表格数据为过滤后的数据
            if (finalFilteredData.length !== finalTableData.length) {
              // 如果过滤后没有数据，保留一个空行
              const dataToLoad = finalFilteredData.length > 0 ? finalFilteredData : [{}];
              tableRef.value?.loadData(dataToLoad);
            }

            // 再调用saveDj保存单据
            const result = await templateStore.saveDj(
              cachedEventType.value || djEventType.value,
              query.bizId,
            );
            if (result && result.success) {
              // 存储返回的挂账编号，用于后续打印功能
              if (result.gzbh) {
                savedGzbh.value = result.danjbh;
              }
              clearGzModalOpen.value = true;
            }
          }
        }
        break;
      case '辅助功能':
        console.log('辅助功能');
        break;
      case '新增单据':
        xFormRef.value.resetForm();
        newDj();
        break;
      case 'djexit':
        handleCloseCurrent();
        break;
      case '记录查找':
      case '记录过滤':
        if (!addedFilter.value) {
          gridOptions.columns = addFilter(gridOptions.columns);
          addedFilter.value = !addedFilter.value;
          return;
        }
        break;
      case 'djpreview':
        try {
          // 获取单据类型
          const djlx = templateStore.djlx;
          // 获取打印方案列表，业务类型设置为 0（表示单据类型）
          const response = await getPrintSchemes('0', djlx);
          printSchemes.value = response;

          // 根据打印方案数量决定处理方式
          if (response.length === 0) {
            message.error('没有找到可用的打印方案');
          } else if (response.length === 1) {
            // 只有一个打印方案，直接选择并执行打印
            await handlePrintSchemeSelect(response[0]);
          } else {
            // 多个打印方案，显示弹窗让用户选择
            printSchemeModalOpen.value = true;
          }
        } catch (err) {
          console.error('获取打印方案列表失败:', err);
          message.error('获取打印方案列表失败，请稍后重试');
        }
        break;
      case 'ExcelIMP_orderinput':
        // EXCEL导入功能
        if (menu.code.startsWith('ExcelIMP_')) {
          currentExcelImportMenuCode.value = menu.code;
          excelImportModalOpen.value = true;
          return;
        }
        break;
      case 'getddwinfo':
        // 单位详情已在函数开头处理
        break;
      case 'getdspinfo':
        // 商品详情已在函数开头处理
        break;
      case 'djlxfilecomm':
        // 附件管理功能
        fileManagementModalOpen.value = true;
        break;
      default:
        if (event.functionName) handleFormEvent(event.functionName, {}, null, eventType);
        break;
    }
  }

  async function handleDataChoose(data: any) {
    // 设置数据检索状态，防止重复挂账
    if (dataRetrievalLock.value) {
      console.log('数据检索正在进行中，忽略重复操作');
      return;
    }

    dataRetrievalLock.value = true;
    isDataRetrieving.value = true;

    try {
      // 先获取后端数据，确保重要字段值不被重置覆盖
      console.log('[handleDataChoose] 开始获取后端数据，gzbh:', data.gzbh);
      await templateStore.fetchTemplateForm(data.gzbh, '');

      // 保存当前重要字段的值
      const currentKbxTable = templateStore.templateForm?.kbxtable || {};
      const preservedFields = {
        danjbh: currentKbxTable.danjbh,
        caozy: currentKbxTable.caozy,
      };

      console.log('[handleDataChoose] 后端返回的重要字段值:', preservedFields);

      // 只重置非重要字段
      const formData = { ...currentKbxTable };
      Object.keys(formData).forEach((key) => {
        if (key !== 'danjbh' && key !== 'caozy' && key !== 'ontime' && key !== 'riqi') {
          // 对于其他字段，如果后端返回了有效值，保留；否则重置为空
          if (formData[key] === null || formData[key] === undefined || formData[key] === '') {
            formData[key] = '';
          }
        }
      });

      // 确保重要字段的值被保留
      if (preservedFields.danjbh !== null && preservedFields.danjbh !== undefined) {
        formData.danjbh = preservedFields.danjbh;
        console.log('[handleDataChoose] 保留 danjbh 字段值:', formData.danjbh);
      }
      if (preservedFields.caozy !== null && preservedFields.caozy !== undefined) {
        formData.caozy = preservedFields.caozy;
        console.log('[handleDataChoose] 保留 caozy 字段值:', formData.caozy);
      }

      // 使用 setFieldsValue 而不是 resetForm，保留重要字段值
      xFormRef.value.setFieldsValue(formData);
      console.log('[handleDataChoose] 表单数据设置完成，最终 formData:', formData);

      djgzModalOpen.value = false;
      tableRef.value?.commitProxy('query');
      // 等待fetchTemplateForm完成后，保存规则到服务器
      const storageKey = `gz_${templateStore.djlx}`;
      await templateStore.saveGzToServer(storageKey);
    } finally {
      // 延迟重置状态，确保所有相关操作完成
      setTimeout(() => {
        isDataRetrieving.value = false;
        dataRetrievalLock.value = false;
      }, 500);
    }
  }

  // 只清空页面数据，不调用后端清空接口
  async function clearPageDataOnly() {
    try {
      const djlx = templateStore.djlx;
      const djbs = templateStore.djbs;

      // 获取初始值
      const response = await getInitValueApi(djlx, 0);
      const initValues = response || {};

      // 重置表单,保留默认值
      const formData = {};
      djInfo.value.kbxTableList
        .filter(
          (i) => i.itemRender.name !== 'AButton' && i.itemRender.name !== 'AText' && i.isActive,
        )
        .forEach((item) => {
          // 根据字段类型设置默认值
          let defaultValue = initValues[item.fieldName] || null;

          // 如果没有初始值，根据字段类型设置默认值
          if (defaultValue === null || defaultValue === undefined || defaultValue === '') {
            if (item.fldType === '1') {
              // 实数类型默认为0
              defaultValue = 0;
            } else if (item.fldType === '2') {
              // 整数类型默认为0
              defaultValue = 0;
            }
          }

          formData[item.fieldName] = defaultValue;
        });

      xFormRef.value.setFieldsValue(formData);

      // 重置表格数据为一个空行
      const emptyRow = {};
      // 为明细表的数值型字段设置默认值
      djInfo.value.mxTableList?.forEach((item) => {
        // 如果是表达式字段，不设置默认值（让表达式计算结果决定）
        if (item.expContent && item.expContent.trim() !== '') {
          // 表达式字段先设置为null，后续由表达式计算
          emptyRow[item.field] = null;
          return;
        }

        if (item.fldType === '1' || item.fldType === 1) {
          // 实数类型默认为0
          emptyRow[item.field] = 0;
        } else if (item.fldType === '2' || item.fldType === 2) {
          // 整数类型默认为0
          emptyRow[item.field] = 0;
        } else {
          // 其他类型默认为null
          emptyRow[item.field] = null;
        }
      });
      tableRef.value?.loadData([emptyRow]);

      // 重置子表格
      sonMxGridOptions.value = null;

      // 清空勾兑方式记录
      gouduiMethodMap.value.clear();
      // 持久化清除操作
      saveGouduiMethodsToStorage();

      // 清除本地缓存（但不调用后端清空接口）
      const storageKey = `gz_${djlx}`;
      const existingDataStr = localStorage.getItem('gz_data') || '{}';
      const existingData = JSON.parse(existingDataStr);
      if (existingData[storageKey]) {
        delete existingData[storageKey];
        localStorage.setItem('gz_data', JSON.stringify(existingData));
      }

      templateStore.setTemplateForm({
        kbxtable: { ...formData, ...initValues },
        mxtable: [{}],
      });
      // 清除挂账编号等状态
      templateStore.setGzbh('');
      // 保存到localStorage
      await templateStore.saveGz();

      // 设置焦点到第一个输入框
      nextTick(() => {
        xFormRef.value.jumpToFirstInput();
      });
    } catch (err) {
      console.error('清空页面数据失败:', err);
      Modal.warn({
        title: '清空失败',
        content: err.message || '清空页面数据失败',
        okText: '确定',
      });
    }
  }

  async function newDj() {
    // 重置所有状态
    xFormRef.value.resetForm();
    sonMxGridOptions.value = null;
    sonMxTable.value = null;
    preGzbh.value = '';
    // 清空勾兑方式记录
    gouduiMethodMap.value.clear();
    // 持久化清除操作
    saveGouduiMethodsToStorage();
    templateStore.setAllNull();

    // 准备新单据数据
    const mxTableDataList: Record<string, any>[] = [];
    const mxTableData = {};
    djInfo.value.mxTableList?.forEach((item: any) => {
      // 所有字段初始化为null，不设置默认值0
      // 只有在调用接口时才将数值型字段的空值转换为0
      mxTableData[item.field] = null;
    });
    mxTableDataList.push(mxTableData);

    const kbxTableData = {};
    djInfo.value.kbxTableList
      .filter((i) => i.itemRender.name !== 'AButton' && i.itemRender.name !== 'AText' && i.isActive)
      .forEach((item: any) => {
        // 根据字段类型设置默认值
        if (item.fldType === '1' || item.fldType === 1) {
          // 实数类型默认为0
          kbxTableData[item.fieldName] = 0;
        } else if (item.fldType === '2' || item.fldType === 2) {
          // 整数类型默认为0
          kbxTableData[item.fieldName] = 0;
        } else {
          // 其他类型默认为null
          kbxTableData[item.fieldName] = null;
        }
      });
    await templateStore.initGz({
      djlx: djlx.value + '',
      djbs: djbs.value,
      kbxTableData,
      mxTableDataList,
    });
    tableRef.value?.commitProxy('query');
    clearGzModalOpen.value = false;
  }

  async function handleQldjgzChoose(data: Array<any>) {
    const gzbh = templateStore.gzbh;

    // 检查localStorage中的缓存
    const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
    const existingDataStr = localStorage.getItem('gz_data') || '{}';
    const existingData = JSON.parse(existingDataStr);
    const cachedData = existingData[storageKey]?.data;

    // 清理服务器数据
    await templateStore.clearGz(data.map((item) => item.gzbh));

    // 清除勾兑方式记录
    data.forEach((item) => {
      if (gouduiMethodMap.value.has(item.gzbh)) {
        gouduiMethodMap.value.delete(item.gzbh);
      }
    });
    // 持久化清除操作
    saveGouduiMethodsToStorage();

    // 如果当前gzbh在清除列表中,且缓存中有数据
    if (data.map((item) => item.gzbh).includes(gzbh) && cachedData) {
      // 清除缓存
      delete existingData[storageKey];
      localStorage.setItem('gz_data', JSON.stringify(existingData));

      // 重置表单
      xFormRef.value.resetForm();

      // 初始化新数据
      await templateStore.initGz({
        djlx: djlx.value,
        djbs: djbs.value,
        kbxTableData: {},
        mxTableDataList: [{}],
      });
    }

    Modal.success({
      title: '操作成功',
      content: '清除挂账成功',
      okText: '确定',
    });
    qlDjGzModalOpen.value = false;
  }

  const handleFormEvent = debounce((funcName: string, event: any, row: any, eventType: any) => {
    // 确保在调用时再次检查弹窗状态
    if (zljsModalOpen.value) return;
    handleFormEventTemp(funcName, event, row, eventType);
  }, 300);

  const handleFormInputEvent = debounce(
    (funcName: string, event: any, row: any, eventType: any, fieldName: string) => {
      // 确保在调用时再次检查弹窗状态
      if (zljsModalOpen.value) return;
      currentFieldName.value = fieldName;
      dataChooseOption.value = 'Form';
      handleFormEventTemp(funcName, event, row, eventType);
    },
    300,
  );

  function jumpToTableFirstInput() {
    const firstEditableColumnIndex = gridOptions.columns.findIndex(
      (col) => col.editRender?.name === 'AInput',
    );
    if (firstEditableColumnIndex !== -1) {
      const firstRow = tableRef.value?.getTableData().fullData[0];
      // 设置焦点到新行的第一个可编辑单元格
      tableRef.value?.setEditCell(firstRow, gridOptions.columns[firstEditableColumnIndex].field);
    }
  }

  async function handleFormEventTemp(funcName: string, event: any, row: any, eventType: any) {
    // 如果当前有弹窗或者处于事件锁定状态，不执行后续操作
    if (zljsModalOpen.value || enterEventLock.value) return;

    // 判断是否是商品档案查询函数
    if (funcName === 'getdspinfo') {
      // 获取当前行数据
      const currentRow =
        row !== null && row !== undefined && typeof row === 'number'
          ? tableRef.value?.getTableData().fullData[row]
          : row;

      if (!currentRow || !currentRow.dspid) {
        Modal.warn({
          title: '查询错误',
          content: '未找到商品编号，请先选择商品',
          okText: '确定',
        });
        return;
      }

      try {
        // 调用档案资料查询接口
        const response = await queryArchiveDataApi({ dspid: currentRow.dspid });
        // 保存数据
        ziliaoData.value = response;
        // 打开详情弹窗
        ziliaoModalOpen.value = true;
      } catch (error) {
        console.error('商品档案查询失败:', error);
        Modal.warn({
          title: '查询错误',
          content: '商品档案查询失败，请稍后重试',
          okText: '确定',
        });
      }
      return;
    }

    // 判断是否是往来单位档案查询函数
    if (funcName === 'getddwinfo') {
      // 获取当前行数据
      const currentRow =
        row !== null && row !== undefined && typeof row === 'number'
          ? tableRef.value?.getTableData().fullData[row]
          : row;

      try {
        // 调用档案资料查询接口
        const response = await queryArchiveDataApi({ ddwid: currentRow.ddwid });

        // 保存数据
        ziliaoData.value = response.data;
        // 打开详情弹窗
        ziliaoModalOpen.value = true;
      } catch (error) {
        console.error('往来单位档案查询失败:', error);
        Modal.warn({
          title: '查询错误',
          content: '往来单位档案查询失败，请稍后重试',
          okText: '确定',
        });
      }
      return;
    }

    // 判断是否是勾兑函数
    if (funcName.startsWith('get_goudui_')) {
      // 添加日志跟踪勾兑函数调用
      console.log('【勾兑】检测到勾兑函数调用:', funcName);

      // 特殊处理 get_goudui_bugdjs 函数
      if (funcName === 'get_goudui_bugdjs') {
        console.log('【勾兑】检测到 bugdjs 特殊勾兑函数，允许用户手动输入');

        // 获取当前挂账编号
        const currentGzbh = templateStore.gzbh;

        // 记录使用了 bugdjs 勾兑方式，但不调用接口，不打开弹窗
        if (currentGzbh) {
          // 校验勾兑方式是否一致
          if (gouduiMethodMap.value.has(currentGzbh)) {
            const recordedMethod = gouduiMethodMap.value.get(currentGzbh);
            if (recordedMethod && recordedMethod !== funcName) {
              // 已经使用了不同的勾兑方式，显示错误提示
              Modal.warn({
                title: '勾兑方式冲突',
                content: `当前单据已使用"${recordedMethod.replace('get_goudui_', '')}"方式勾兑，不能切换为"${funcName.replace('get_goudui_', '')}"方式`,
                okText: '确定',
              });
              return;
            }
          }

          // 记录当前勾兑方式
          gouduiMethodMap.value.set(currentGzbh, funcName);
          // 持久化保存到localStorage
          saveGouduiMethodsToStorage();
          console.log('【勾兑】记录 bugdjs 勾兑方式:', { gzbh: currentGzbh, method: funcName });
        }

        // 直接返回，允许用户手动输入，不调用接口，不打开弹窗
        return;
      }

      // 获取当前挂账编号
      const currentGzbh = templateStore.gzbh;

      // 校验勾兑方式是否一致
      if (currentGzbh && gouduiMethodMap.value.has(currentGzbh)) {
        const recordedMethod = gouduiMethodMap.value.get(currentGzbh);
        if (recordedMethod && recordedMethod !== funcName) {
          // 已经使用了不同的勾兑方式，显示错误提示
          Modal.warn({
            title: '勾兑方式冲突',
            content: `当前单据已使用"${recordedMethod.replace('get_goudui_', '')}"方式勾兑，不能切换为"${funcName.replace('get_goudui_', '')}"方式`,
            okText: '确定',
          });
          return;
        }
      }

      // 根据funcName获取funcInfo
      const funcInfo = templateStore.funcinfo;
      let params = {};
      funcInfo[funcName]?.forEach((item: any) => {
        if (item === 'para') {
          params = { ...params, para: event?.target?.value };
        } else if (item === 'p_fdname') {
          // 如果参数名称为p_fdname，则该字段的值为当前字段的字段英文名
          params = { ...params, p_fdname: currentFieldName.value };
        } else if (item.startsWith('mxtb.')) {
          const mxtable = tableRef.value?.getTableData().fullData[row];
          params = {
            ...params,
            [item]: mxtable[item.replace('mxtb.', '')],
          };
        } else if (item.startsWith('hztb.')) {
          params = {
            ...params,
            [item]: templateStore.templateForm.kbxtable[item.replace('hztb.', '')],
          };
        } else if (item === 'djlxbs') {
          params = { ...params, djlxbs: templateStore['djlx'] };
        } else {
          params = { ...params, [item]: templateStore[item] };
        }
      });

      console.log('【勾兑】构建的请求参数:', params);

      // 调用勾兑接口
      try {
        console.log('【勾兑】开始调用勾兑接口');
        const resp = await extractGouduiRawApi({
          funcname: funcName,
          params: params,
          pageNum: 1,
          pageSize: 50,
        });

        console.log('【勾兑】接收到API响应:', resp);
        console.log('【勾兑】响应数据结构:', {
          hasData: !!resp.data,
          dataType: typeof resp.data,
          dataKeys: resp.data ? Object.keys(resp.data) : 'none',
          nestedData: resp.data?.data ? '存在' : '不存在',
          code: resp.data?.code || resp.data?.data?.code,
        });

        // 判断响应结构，适配两种可能的结构
        // 1. resp.data = { code, message, data }
        // 2. resp.data = { data: { code, message, data } }
        const responseData = resp.data;

        if (responseData.code === '0000') {
          // 记录当前勾兑方式
          if (currentGzbh) {
            gouduiMethodMap.value.set(currentGzbh, funcName);
            // 持久化保存到localStorage
            saveGouduiMethodsToStorage();
            console.log('【勾兑】记录勾兑方式:', { gzbh: currentGzbh, method: funcName });
          }

          console.log('【勾兑】响应成功，准备处理数据');
          // 保存行号
          mxTableRow.value = row;

          // 设置勾兑方案数据
          gouduiBody.value = responseData.data;
          console.log('【勾兑】设置body数据:', gouduiBody.value);

          // 处理需要隐藏的字段
          buxszd.value = gouduiBody.value.buxszd || [];

          // 保存字段名
          currentFieldName.value = currentFieldName.value || event?.target?.dataset?.field;
          console.log('【勾兑】当前字段名:', currentFieldName.value);

          // 打开勾兑方案弹窗
          console.log('【勾兑】即将打开弹窗');
          gouduiModalOpen.value = true;
          console.log('【勾兑】gouduiModalOpen值设置为:', gouduiModalOpen.value);

          // 确保弹窗状态更新
          nextTick(() => {
            console.log(
              '【勾兑】nextTick后弹窗状态:',
              gouduiModalOpen.value,
              '弹窗组件是否存在:',
              !!document.querySelector('.goudui-modal'),
            );
          });
        } else {
          console.error('【勾兑】API返回错误:', responseData.message);
          // 创建错误弹窗
          const modal = Modal.warn({
            title: '错误',
            content: responseData.message,
            okText: '确定',
            onOk: () => {
              // 延迟一帧后将焦点返回到之前的输入框
              setTimeout(() => {
                // 如果是表格区域的字段
                if (dataChooseOption.value === 'Table' && lastClickColumn.value) {
                  // 检查mxTableRow.value
                  if (
                    mxTableRow.value !== null &&
                    mxTableRow.value !== undefined &&
                    tableRef.value?.getTableData()?.fullData &&
                    mxTableRow.value < tableRef.value.getTableData().fullData.length
                  ) {
                    tableRef.value.setEditCell(
                      tableRef.value.getTableData().fullData[mxTableRow.value],
                      lastClickColumn.value.field,
                    );
                  } else {
                    // 如果无法通过mxTableRow定位，尝试获取当前选中行或第一行
                    const currentRow =
                      tableRef.value?.getCurrentRecord() ||
                      (tableRef.value?.getTableData().fullData.length > 0
                        ? tableRef.value.getTableData().fullData[0]
                        : null);
                    if (currentRow) {
                      tableRef.value.setEditCell(currentRow, lastClickColumn.value.field);
                    }
                  }
                } else {
                  // 如果是表单区域的字段或其他元素
                  if (document.activeElement) {
                    (document.activeElement as HTMLElement).focus();
                  }
                }
              }, 0);
            },
          });
        }
      } catch (error) {
        console.error('【勾兑】API调用错误:', error);
        Modal.warn({
          title: '接口错误',
          content: '调用勾兑接口失败: ' + (error instanceof Error ? error.message : '未知错误'),
        });
      }
      return;
    }

    // 原有的处理逻辑
    // 根据funcName获取出templateStore中的funcInfo，根据funcInfo list遍历key值
    const funcInfo = templateStore.funcinfo;
    let params = {};
    funcInfo[funcName]?.forEach((item: any) => {
      if (item === 'para') {
        params = { ...params, para: event?.target?.value };
      } else if (item === 'p_fdname') {
        // 如果参数名称为p_fdname，则该字段的值为当前字段的字段英文名
        params = { ...params, p_fdname: currentFieldName.value };
      } else if (item.startsWith('mxtb.')) {
        const mxtable = tableRef.value?.getTableData().fullData[row];
        const fieldName = item.replace('mxtb.', '');
        // 确保mxtable存在，否则可能导致undefined错误
        let value = mxtable ? mxtable[fieldName] : null;

        const fieldConfig = djInfo.value?.mxTableList?.find((c) => c.field === fieldName);
        if (
          fieldConfig &&
          (fieldConfig.fldType === '1' || fieldConfig.fldType === '2') &&
          (value === '' || value === null || value === undefined)
        ) {
          value = 0;
        }
        params = { ...params, [item]: value };
      } else if (item.startsWith('hztb.')) {
        const fieldName = item.replace('hztb.', '');
        // 确保templateStore.templateForm.kbxtable存在
        let value = templateStore.templateForm?.kbxtable?.[fieldName];

        const fieldConfig = djInfo.value?.kbxTableList?.find((c) => c.fieldName === fieldName);
        if (
          fieldConfig &&
          (fieldConfig.fldType === '1' || fieldConfig.fldType === '2') &&
          (value === '' || value === null || value === undefined)
        ) {
          value = 0;
        }
        params = { ...params, [item]: value };
      } else if (item === 'djlxbs') {
        params = { ...params, djlxbs: templateStore['djlx'] };
      } else {
        params = { ...params, [item]: templateStore[item] };
      }
    });

    console.log(`提取单据 - 调用API: ${funcName}`, params);
    const resp = await extractRawApi({
      funcname: funcName,
      params: params,
      pageNum: 1,
      pageSize: 50,
    });

    console.log(`提取单据 - API响应:`, resp.data);
    if (resp.data.code == '0000') {
      zljsBody.value = resp.data.data;
      // 保存当前函数名，用于后续操作
      handleFuncName.value = funcName;
    } else {
      // 保存当前活跃元素
      const activeElement = document.activeElement as HTMLElement;
      // 创建modal实例并保存引用
      const modal = Modal.warn({
        title: '错误',
        content: resp.data.message,
        okText: '确定',
        keyboard: false, // 禁用默认的键盘行为
        maskClosable: false, // 禁止点击遮罩关闭
        onOk: () => {
          // 延迟一帧后将焦点返回到之前的输入框
          setTimeout(() => {
            // 如果是表格区域的字段
            if (dataChooseOption.value === 'Table' && lastClickColumn.value) {
              // 移除debugger语句
              // 添加对mxTableRow.value的检查
              if (
                mxTableRow.value !== null &&
                mxTableRow.value !== undefined &&
                tableRef.value?.getTableData()?.fullData &&
                mxTableRow.value < tableRef.value.getTableData().fullData.length
              ) {
                tableRef.value.setEditCell(
                  tableRef.value.getTableData().fullData[mxTableRow.value],
                  lastClickColumn.value.field,
                );
              } else {
                // 如果无法通过mxTableRow定位，尝试获取当前选中行或第一行
                const currentRow =
                  tableRef.value?.getCurrentRecord() ||
                  (tableRef.value?.getTableData().fullData.length > 0
                    ? tableRef.value.getTableData().fullData[0]
                    : null);
                if (currentRow) {
                  tableRef.value.setEditCell(currentRow, lastClickColumn.value.field);
                }
              }
            } else {
              // 如果是表单区域的字段或其他元素
              if (activeElement) {
                activeElement.focus();
              }
            }
          }, 0);
        },
      });

      // 添加自定义键盘事件处理
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter') {
          modal.destroy(); // 关闭模态框
          document.removeEventListener('keydown', handleKeyDown); // 移除事件监听
          // 延迟一帧后将焦点返回到之前的输入框
          setTimeout(() => {
            // 如果是表格区域的字段
            if (dataChooseOption.value === 'Table' && lastClickColumn.value) {
              // 移除debugger语句
              // 添加对mxTableRow.value的检查
              if (
                mxTableRow.value !== null &&
                mxTableRow.value !== undefined &&
                tableRef.value?.getTableData()?.fullData &&
                mxTableRow.value < tableRef.value.getTableData().fullData.length
              ) {
                tableRef.value.setEditCell(
                  tableRef.value.getTableData().fullData[mxTableRow.value],
                  lastClickColumn.value.field,
                );
              } else {
                // 如果无法通过mxTableRow定位，尝试获取当前选中行或第一行
                const currentRow =
                  tableRef.value?.getCurrentRecord() ||
                  (tableRef.value?.getTableData().fullData.length > 0
                    ? tableRef.value.getTableData().fullData[0]
                    : null);
                if (currentRow) {
                  tableRef.value.setEditCell(currentRow, lastClickColumn.value.field);
                }
              }
            } else {
              // 如果是表单区域的字段或其他元素
              if (activeElement) {
                activeElement.focus();
              }
            }
          }, 0);
        }
      };
      document.addEventListener('keydown', handleKeyDown);
      return;
    }
    mxTableRow.value = row;

    if (zljsBody.value && (zljsBody.value.tablevalue || zljsBody.value.pagedData?.data)) {
      // 保存函数名和参数，用于后续分页查询
      zljsBody.value.funcname = funcName;
      zljsBody.value.params = params;

      buxszd.value = zljsBody.value.buxszd;
      if (eventType && zljsBody.value.jstype == '0') {
        djEventType.value = eventType;
        cachedEventType.value = eventType; // 缓存用户操作类型（2或3）
        handleFuncName.value = funcName;
      } else {
        djEventType.value = 1;
      }
      const tableValue = zljsBody.value.tablevalue || zljsBody.value.pagedData?.data || [];
      if (zljsBody.value.isdhff && tableValue.length === 1) {
        const row = tableValue[0];
        // 检查 jsjy 字段
        if (row.jsjy === '1' || row.jsjy === '2') {
          // 处理 jsinfo 中的换行符
          const formattedInfo = row.jsinfo ? row.jsinfo.replace(/\\r/g, '\n') : '提示信息';
          Modal.confirm({
            title: '提示',
            content: formattedInfo,
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              if (row.jsjy === '1') {
                // jsjy 为 1 时，继续执行选择逻辑
                zljsDataChoose(row, mxTableRow.value, currentFieldName.value);
              }
              // jsjy 为 2 时，不执行任何操作，打开模态框让用户继续选择
              if (row.jsjy === '2') {
                zljsModalOpen.value = true;
              }
            },
            onCancel: () => {
              // 取消时打开模态框让用户重新选择
              zljsModalOpen.value = true;
            },
          });
        } else {
          // 正常选择逻辑
          zljsDataChoose(row, mxTableRow.value, currentFieldName.value);
        }
      } else {
        zljsModalOpen.value = true;
      }
    } else {
      const modal = Modal.warning({
        title: '提示',
        content: '没有可提取的单据',
        okText: '确定',
        keyboard: false, // 禁用默认的键盘行为
        maskClosable: false, // 禁止点击遮罩关闭
        onOk: () => {
          // 延迟一帧后重新聚焦到表格
          setTimeout(() => {
            // 如果是表格区域的字段
            if (dataChooseOption.value === 'Table' && lastClickColumn.value) {
              // 移除debugger语句
              // 添加对mxTableRow.value的检查
              if (
                mxTableRow.value !== null &&
                mxTableRow.value !== undefined &&
                tableRef.value?.getTableData()?.fullData &&
                mxTableRow.value < tableRef.value.getTableData().fullData.length
              ) {
                tableRef.value.setEditCell(
                  tableRef.value.getTableData().fullData[mxTableRow.value],
                  lastClickColumn.value.field,
                );
              } else {
                // 如果无法通过mxTableRow定位，尝试获取当前选中行或第一行
                const currentRow =
                  tableRef.value?.getCurrentRecord() ||
                  (tableRef.value?.getTableData().fullData.length > 0
                    ? tableRef.value.getTableData().fullData[0]
                    : null);
                if (currentRow) {
                  tableRef.value.setEditCell(currentRow, lastClickColumn.value.field);
                }
              }
            } else {
              // 如果是表单区域的字段或其他元素
              const activeElement = document.activeElement as HTMLElement;
              if (activeElement) {
                activeElement.focus();
              }
            }
          }, 0);
        },
      });

      // 添加自定义键盘事件处理
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter') {
          modal.destroy();
          document.removeEventListener('keydown', handleKeyDown);
          // 延迟一帧后重新聚焦
          setTimeout(() => {
            // 如果是表格区域的字段
            if (dataChooseOption.value === 'Table' && lastClickColumn.value) {
              // 移除debugger语句
              // 添加对mxTableRow.value的检查
              if (
                mxTableRow.value !== null &&
                mxTableRow.value !== undefined &&
                tableRef.value?.getTableData()?.fullData &&
                mxTableRow.value < tableRef.value.getTableData().fullData.length
              ) {
                tableRef.value.setEditCell(
                  tableRef.value.getTableData().fullData[mxTableRow.value],
                  lastClickColumn.value.field,
                );
              } else {
                // 如果无法通过mxTableRow定位，尝试获取当前选中行或第一行
                const currentRow =
                  tableRef.value?.getCurrentRecord() ||
                  (tableRef.value?.getTableData().fullData.length > 0
                    ? tableRef.value.getTableData().fullData[0]
                    : null);
                if (currentRow) {
                  tableRef.value.setEditCell(currentRow, lastClickColumn.value.field);
                }
              }
            } else {
              // 如果是表单区域的字段或其他元素
              const activeElement = document.activeElement as HTMLElement;
              if (activeElement) {
                activeElement.focus();
              }
            }
          }, 0);
        }
      };
      document.addEventListener('keydown', handleKeyDown);
    }
  }
  // 查询单据
  async function selectDj(data: any, targetField: string) {
    const funcInfo = templateStore.funcinfo;
    let params = {};
    // 保存当前函数名，确保清空后再次提取时能够使用
    const currentHandleFuncName = handleFuncName.value;

    // 确保函数名存在
    if (!currentHandleFuncName || !funcInfo[currentHandleFuncName]) {
      console.error('提取单据失败: 函数名不存在', currentHandleFuncName);
      modalValidated.value = false;
      return;
    }

    funcInfo[currentHandleFuncName]?.forEach((item: any) => {
      if (item === 'p_fdname') {
        // 如果参数名称为p_fdname，则该字段的值为当前字段的字段英文名
        params = { ...params, p_fdname: targetField };
      } else if (item.startsWith('hztb.')) {
        const fieldName = item.replace('hztb.', '');
        // 优先使用data中的值，如果不存在则使用templateForm中的值
        const value =
          data && data[fieldName] !== undefined
            ? data[fieldName]
            : templateStore.templateForm?.kbxtable?.[fieldName];
        params = { ...params, [item]: value };
      } else if (item === 'djlxbs') {
        params = { ...params, djlxbs: templateStore['djlx'] };
      } else {
        params = { ...params, [item]: templateStore[item] };
      }
    });
    modalValidated.value = false;

    try {
      // 检查djInfo是否存在，避免在切换djlx时出现错误
      if (!templateStore.djInfo) {
        console.warn('djInfo为空，跳过单据详情获取');
        modalValidated.value = false;
        return;
      }

      // 获取单据详情，确保包含所有字段（包括isactive='N'的字段）
      const result = await templateStore.getDjDetails(
        handleFuncName.value,
        djEventType.value,
        params,
        targetField,
        typeof data === 'string' ? data : data.danjbh,
      );

      // 检查result是否有效
      if (!result) {
        console.warn('获取单据详情返回空结果');
        modalValidated.value = false;
        return;
      }

      // 加载表格数据
      if (tableRef.value) {
        const tableData = result.mxtable || [];
        // 处理表达式字段
        await handleExpressionFields(tableData);
        await tableRef.value.loadData(tableData);
        // 确保dj_rec值正确
        updateRowDjRec();
      }
      // 更新表格数据
      setTimeout(() => {
        // 直接调用挂账，确保数据被保存
        const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
        templateStore.saveGzToServer(storageKey);
        handleTableChange();
      }, 100);
    } catch (error) {
      console.error('获取单据详情失败:', error);
      modalValidated.value = false;

      // 如果是组件渲染错误，尝试重新初始化表单
      if (
        error.message?.includes('emitsOptions') ||
        error.message?.includes('Cannot read properties of null')
      ) {
        console.warn('检测到组件渲染错误，尝试重新初始化表单');
        setTimeout(() => {
          xFormRef.value?.setFieldsValue(templateStore.templateForm?.kbxtable || {});
        }, 100);
      }
    }
  }

  function changeCustomColumn(data: any) {
    customColumnModalOpen.value = false;
    if (data) {
      gridOptions.columns = data;
    }
  }

  async function zljsDataChoose(data: any, mxTableRow: any, targetField: string) {
    if (modalValidated.value) return;
    modalValidated.value = true;

    // 设置数据检索状态
    isDataRetrieving.value = true;

    // 显示数据检索进度提示
    const loadingMessage = message.loading('数据检索中，请稍候...', 0);
    // 判断当前eventType是否非1，如果非1的话则代表是通过顶部菜单或可变项触发的事件
    if (djEventType.value !== 1) {
      // 先清理旧单据
      if (preGzbh.value) {
        await templateStore.clearGzInfo([preGzbh.value]);
      } else {
        await templateStore.clearGzInfo([templateStore.gzbh]);
      }
      preGzbh.value = templateStore.gzbh;
      if (djEventType.value === 2) {
        // 判断是多条数据还是单条数据
        const danjuData = Array.isArray(data) ? data[0] : data;
        // templateStore.setGzbh(danjuData.danjbh);
        preGzbh.value = danjuData.danjbh;
      }
      // 判断是多条数据还是单条数据
      const selectedData = Array.isArray(data) ? data[0] : data;
      templateStore.setDanjbh(selectedData.danjbh);
      selectDj(selectedData, targetField);
      if (djEventType.value === 3) {
        console.log('jumpToNextInput');
        xFormRef.value.jumpToNextInput();
      }
      setTimeout(() => {
        zljsModalOpen.value = false;
        console.log('zljsModalOpen', zljsModalOpen.value);
        // 重置数据检索状态
        isDataRetrieving.value = false;
        // 关闭加载提示
        loadingMessage();
      }, 1000);
      return;
    }

    // 获取表格数据
    const tableData = tableRef.value?.getTableData().fullData;
    const { mxTableList } = templateStore.djInfo;

    // 添加确保每行都有dj_rec的函数
    const ensureRowDjRec = (tableData: any[]) => {
      if (!tableData) return;
      tableData.forEach((row, index) => {
        if (!row.dj_rec) {
          row.dj_rec = index + 1;
        }
      });
    };

    if (dataChooseOption.value === 'Table') {
      if (Array.isArray(data)) {
        // 多选模式
        const insertIndex = mxTableRow;
        const newRows = [];
        //const duplicateErrors = [];

        // 先检查选中的数据之间是否有重复
        /*for (let i = 0; i < data.length; i++) {
          for (let j = i + 1; j < data.length; j++) {
            for (const key in mxTableList) {
              if (mxTableList[key].isChkDup) {
                const fieldName = mxTableList[key].fieldName;
                const value1 = data[i][fieldName];
                const value2 = data[j][fieldName];

                // 跳过空值检查
                if (
                  value1 === null ||
                  value1 === undefined ||
                  value1 === '' ||
                  value2 === null ||
                  value2 === undefined ||
                  value2 === ''
                ) {
                  continue;
                }

                if (value1 === value2) {
                  duplicateErrors.push(
                    `选中的数据中存在重复的${mxTableList[key].fieldTitle}: ${value1}`,
                  );
                  break;
                }
              }
            }
          }
        }

        // 如果选中数据之间有重复，直接返回错误
        if (duplicateErrors.length > 0) {
          Modal.warn({
            title: '数据重复错误',
            content: duplicateErrors[0],
            okText: '确定',
          });
          return;
        }*/

        // 遍历每一行选中的数据，检查与现有数据是否重复
        for (let i = 0; i < data.length; i++) {
          const row = data[i];
          // 检查重复
          /*let hasDuplicate = false;
          for (const key in mxTableList) {
            if (mxTableList[key].isChkDup) {
              const fieldName = mxTableList[key].fieldName;
              const newValue = row[fieldName];

              // 跳过空值检查
              if (newValue === null || newValue === undefined || newValue === '') {
                continue;
              }

              // 检查是否存在重复值（排除当前行，因为它会被覆盖）
              const isDuplicate = tableData.some((existingRow, index) => {
                if (index === insertIndex) return false;
                return existingRow[fieldName] === newValue;
              });

              if (isDuplicate) {
                const modal = Modal.warn({
                  title: '字段重复错误',
                  content: `${mxTableList[key].fieldTitle}不能重复: ${newValue}`,
                  okText: '确定',
                  keyboard: false,
                  maskClosable: false,
                  onOk: () => {
                    document.removeEventListener('keydown', handleKeyDown);
                    modalValidated.value = false;
                  },
                });

                const handleKeyDown = (e: KeyboardEvent) => {
                  if (e.key === 'Enter') {
                    modal.destroy();
                    document.removeEventListener('keydown', handleKeyDown);
                    modalValidated.value = false;
                  }
                };

                document.addEventListener('keydown', handleKeyDown);
                hasDuplicate = true;
                break;
              }
            }
          }

          if (!hasDuplicate) {*/
          // 创建新行数据
          const newRow = { ...row };
          // 保留原始dj_rec值
          if (Object.prototype.hasOwnProperty.call(row, 'dj_rec')) {
            delete newRow.dj_rec;
          }
          Object.keys(row).forEach((key) => {
            if (Object.prototype.hasOwnProperty.call(newRow, key)) {
              newRow[key] = row[key];
            }
          });
          newRows.push(newRow);
          //}
        }

        // 只有当所有数据都通过检查时才进行插入
        if (newRows.length === data.length) {
          // 第一条数据覆盖当前行，其余数据插入到后面
          if (insertIndex >= 0 && tableData && tableData[insertIndex]) {
            tableData[insertIndex] = newRows[0];
            if (newRows.length > 1) {
              tableData.splice(insertIndex + 1, 0, ...newRows.slice(1));
            }
          }

          // 更新表格数据
          tableRef.value?.loadData(tableData).then(async () => {
            // 更新所有行的dj_rec值
            updateRowDjRec();

            // 处理表达式字段
            const startIndex = insertIndex;
            const endIndex = insertIndex + newRows.length - 1;
            await handleExpressionFields(
              tableData,
              Array.from({ length: newRows.length }, (_, i) => startIndex + i),
            );

            // 初始化子表
            newRows.forEach((row) => {
              initSonMxTable(row);
            });

            // 多选数据插入完成后执行挂账操作（只有在非数据检索期间才执行）
            if (!isDataRetrieving.value) {
              try {
                const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
                // 确保所有行都有dj_rec
                ensureRowDjRec(tableData);
                await templateStore.saveGzToServer(storageKey);
                console.log('多选插入数据后挂账成功');
              } catch (error) {
                console.error('多选插入数据后挂账失败:', error);
              }
            } else {
              console.log('数据检索期间，跳过挂账操作（多选模式）');
            }

            // 设置新插入行的选中状态
            newRows.forEach((row) => {
              tableRef.value?.setCheckboxRow(row, true);
            });
          });

          nextFocus(mxTableRow, lastClickColumn.value);

          // 更新表单数据 - 确保包含所有字段，即使是非活动字段
          const templateForm = templateStore.templateForm;
          const kbxTable = templateForm.kbxtable;
          const selectedData = data[0];

          // 创建一个新的kbxtable对象，保留原有数据并添加新数据
          let updatedKbxTable = { ...kbxTable };

          // 遍历所有从资料检索获取的字段，包括isactive='N'的字段
          if (dataChooseOption.value === 'Table') {
            Object.keys(selectedData).forEach((key) => {
              // 排除内部字段和特定的时间字段（ontime、riqi）
              // 保留 danjbh 和 caozy 字段，确保后端返回的值能正确设置
              if (key !== '_X_ROW_KEY' && key !== 'ontime' && key !== 'riqi') {
                // 检查字段是否在kbxTableList中且isMxField为false
                const kbxField = templateStore.djInfo.kbxTableList.find(
                  (field) => field.fieldName === key,
                );
                if (!kbxField || kbxField.isMxField) {
                  updatedKbxTable[key] = selectedData[key];
                }
              }
            });
          } else {
            // 非表格模式也需要排除特定字段
            Object.keys(selectedData).forEach((key) => {
              // 排除内部字段和特定的时间字段（ontime、riqi）
              // 保留 danjbh 和 caozy 字段，确保后端返回的值能正确设置
              if (key !== '_X_ROW_KEY' && key !== 'ontime' && key !== 'riqi') {
                updatedKbxTable[key] = selectedData[key];
              }
            });
          }

          templateStore.setTemplateForm({
            ...templateForm,
            kbxtable: updatedKbxTable,
            mxtable: tableData,
          });

          // 关闭弹窗
          modalValidated.value = false;
          zljsModalOpen.value = false;
          // 重置数据检索状态
          isDataRetrieving.value = false;
          // 关闭加载提示
          loadingMessage();
        }
        return;
      } else {
        // 单选模式 - 保持原有逻辑
        // 检查重复数据
        /*let hasDuplicate = false;
        for (const key in mxTableList) {
          if (mxTableList[key].isChkDup) {
            const fieldName = mxTableList[key].fieldName;
            const newValue = data[fieldName];

            // 跳过空值检查
            if (newValue === null || newValue === undefined || newValue === '') {
              continue;
            }

            // 检查是否存在重复值
            const isDuplicate = tableData?.some((row, index) => {
              // 排除当前编辑行
              if (index === mxTableRow) return false;
              return row[fieldName] === newValue;
            });

            if (isDuplicate) {
              const modal = Modal.warn({
                title: '字段重复错误',
                content: `${mxTableList[key].fieldTitle}不能重复: ${newValue}`,
                okText: '确定',
                keyboard: false,
                maskClosable: false,
                onOk: () => {
                  document.removeEventListener('keydown', handleKeyDown);
                  modalValidated.value = false;
                },
              });

              const handleKeyDown = (e: KeyboardEvent) => {
                if (e.key === 'Enter') {
                  modal.destroy();
                  document.removeEventListener('keydown', handleKeyDown);
                  modalValidated.value = false;
                }
              };

              document.addEventListener('keydown', handleKeyDown);
              hasDuplicate = true;
              break;
            }
          }
        }

        if (hasDuplicate) return;*/

        // 通过索引修改特定行
        if (tableData && tableData[mxTableRow]) {
          const originalDjRec = tableData[mxTableRow].dj_rec;
          const newData = { ...tableData[mxTableRow], ...data };
          if (originalDjRec) {
            newData.dj_rec = originalDjRec;
          }
          tableData[mxTableRow] = newData;
          // 处理表达式字段
          await handleExpressionFields(tableData, [mxTableRow]);
          // 更新表格数据
          tableRef.value?.loadData(tableData);
          // 更新所有行的dj_rec值
          updateRowDjRec();
          initSonMxTable(data);

          // 单选数据插入完成后执行挂账操作（只有在非数据检索期间才执行）
          if (!isDataRetrieving.value) {
            try {
              const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
              // 确保所有行都有dj_rec
              ensureRowDjRec(tableData);
              await templateStore.saveGzToServer(storageKey);
              console.log('单选插入数据后挂账成功');
            } catch (error) {
              console.error('单选插入数据后挂账失败:', error);
            }
          } else {
            console.log('数据检索期间，跳过挂账操作（单选模式）');
          }
        }
        nextFocus(mxTableRow, lastClickColumn.value, true);
      }
    } else {
      // 非表格模式下也执行挂账操作（只有在非数据检索期间才执行）
      if (!isDataRetrieving.value) {
        try {
          const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
          // 确保所有行都有dj_rec
          ensureRowDjRec(tableData);
          await templateStore.saveGzToServer(storageKey);
          console.log('非表格模式数据选择后挂账成功');
        } catch (error) {
          console.error('非表格模式数据选择后挂账失败:', error);
        }
      } else {
        console.log('数据检索期间，跳过挂账操作（非表格模式）');
      }

      setTimeout(() => {
        xFormRef.value.jumpToNextInput();
      }, 100);
    }

    modalValidated.value = false;
    zljsModalOpen.value = false;
    // 重置数据检索状态
    isDataRetrieving.value = false;
    // 关闭加载提示
    loadingMessage();

    // 将获取到的data复制给表单 - 确保包含所有字段，即使是非活动字段
    const templateForm = templateStore.templateForm;
    const kbxTable = templateForm.kbxtable;

    // 处理单选或多选的情况
    const selectedData = Array.isArray(data) ? data[0] : data;

    // 创建一个新的kbxtable对象，保留原有数据
    let updatedKbxTable = { ...kbxTable };

    // 遍历所有从资料检索获取的字段，包括isactive='N'的字段
    if (dataChooseOption.value === 'Table') {
      Object.keys(selectedData).forEach((key) => {
        // 排除内部字段和特定的四个字段（ontime、riqi、danjbh、caozy）
        if (
          key !== '_X_ROW_KEY' &&
          key !== 'ontime' &&
          key !== 'riqi' &&
          key !== 'danjbh' &&
          key !== 'caozy'
        ) {
          // 检查字段是否在kbxTableList中且isMxField为false
          const kbxField = templateStore.djInfo.kbxTableList.find(
            (field) => field.fieldName === key,
          );
          if (!kbxField || kbxField.isMxField) {
            updatedKbxTable[key] = selectedData[key];
          }
        }
      });
    } else {
      // 非表格模式也需要排除特定字段
      Object.keys(selectedData).forEach((key) => {
        // 排除内部字段和特定的四个字段（ontime、riqi、danjbh、caozy）
        if (
          key !== '_X_ROW_KEY' &&
          key !== 'ontime' &&
          key !== 'riqi' &&
          key !== 'danjbh' &&
          key !== 'caozy'
        ) {
          updatedKbxTable[key] = selectedData[key];
        }
      });
    }

    // 更新表单数据
    templateStore.setTemplateForm({
      ...templateForm,
      kbxtable: updatedKbxTable,
      mxtable: tableData,
    });

    // 数据检索完成后，执行一次完整的挂账操作
    try {
      console.log('数据检索完成，执行最终挂账操作');
      const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
      await templateStore.saveGzToServer(storageKey);
      console.log('数据检索最终挂账成功');
    } catch (error) {
      console.error('数据检索最终挂账失败:', error);
    }
  }

  // 跳转到下一光标
  function nextFocus(rowIndex, column, createNewRow = false) {
    const row = tableRef.value?.getTableData().fullData[rowIndex];
    // 获取当前字段配置
    const { mxTableList } = templateStore.djInfo || {};
    const currentFieldConfig = mxTableList?.find((item) => item.fieldName === column.field);

    // 如果找不到当前字段配置或者没有nextFldName，使用默认的下一列逻辑
    if (!currentFieldConfig || !currentFieldConfig.nextFldName) {
      // 获取当前行和列的索引
      const columnIndex = gridOptions.columns.findIndex((col) => col?.field === column?.field);
      // 找到下一个可编辑的列
      let nextEditableColumnIndex = columnIndex + 1;
      while (
        nextEditableColumnIndex < gridOptions.columns.length &&
        (!gridOptions.columns[nextEditableColumnIndex].editRender ||
          gridOptions.columns[nextEditableColumnIndex].editRender.name !== 'AInput')
      ) {
        nextEditableColumnIndex++;
      }
      if (nextEditableColumnIndex < gridOptions.columns.length) {
        // 移动到同一行的下一列可编辑单元格
        nextTick(() => {
          tableRef.value?.setEditCell(row, gridOptions.columns[nextEditableColumnIndex].field);
        });
        return;
      } else if (rowIndex < tableRef.value?.getTableData().fullData.length - 1) {
        // 如果是行的最后一列，移动到下一行的第一个可编辑单元格
        const nextRow = tableRef.value?.getTableData().fullData[rowIndex + 1];
        const firstEditableColumnIndex = gridOptions.columns.findIndex(
          (col: any) => col.editRender,
        );
        if (firstEditableColumnIndex !== -1) {
          nextTick(() => {
            tableRef.value?.setEditCell(nextRow, gridOptions.columns[firstEditableColumnIndex]);
          });
        }
        return;
      }
      return;
    }

    // 处理 nextFldName 的逻辑
    if (currentFieldConfig.nextFldName === 'keydown') {
      // 检查当前行是否为空行，如果是空行则不新增
      if (isRowEmpty(row)) {
        return;
      }

      // 检查是否已存在其他空行，如果存在则跳转到空行的第一个输入框
      const tableData5 = tableRef.value?.getTableData().fullData;
      const hasEmptyRowInTable5 = tableData5 && tableData5.some((row) => isRowEmpty(row));
      if (hasEmptyRowInTable5) {
        jumpToFirstEmptyRow();
        return;
      }

      // 打印明细项换行的debug日志
      console.log('🔄 [DEBUG] 明细项换行 - 创建新行', {
        timestamp: new Date().toISOString(),
        currentRow: row,
        currentColumn: column?.field,
        fieldConfig: currentFieldConfig,
      });

      // 如果nextFldName是keydown，则创建新行
      tableRef.value?.insertAt({}, -1).then(() => {
        // 获取第一个可编辑的列
        const firstEditableColumnIndex = gridOptions.columns.findIndex(
          (col: any) => col.editRender,
        );
        if (firstEditableColumnIndex !== -1) {
          // 获取新行（最后一行）
          const newRowIndex = tableRef.value?.getTableData().fullData.length - 1;
          const newRow = tableRef.value?.getTableData().fullData[newRowIndex];
          nextTick(() => {
            tableRef.value?.setEditCell(
              newRow,
              gridOptions.columns[firstEditableColumnIndex].field,
            );
          });
        }
      });
      return;
    } else {
      // 根据nextFldName找到对应的列
      const nextColumnIndex = gridOptions.columns.findIndex(
        (col) => col.field === currentFieldConfig.nextFldName,
      );
      if (nextColumnIndex !== -1) {
        // 移动到指定的字段
        nextTick(() => {
          tableRef.value?.setEditCell(row, gridOptions.columns[nextColumnIndex].field);
        });
        return;
      }
    }
  }

  function handleTableChange() {
    // 更新表格数据中的dj_rec值
    updateRowDjRec();

    // 原有逻辑
    const mxTableData = tableRef.value?.getTableData().fullData || [];
    templateStore.setTemplateForm({
      ...templateStore.templateForm,
      mxtable: mxTableData,
    });
  }

  // 处理同行输入框切换时的校验逻辑
  const handleCellValidationForSwitch = async (
    row: any,
    column: any,
    rowIndex: number,
    _newRowIndex: number | null,
    _newFieldName: string | null,
  ) => {
    // 防止重复处理
    if (isValidating.value) return;

    // 检查是否有Modal弹窗打开
    if (modalOpen.value || zljsModalOpen.value) return;

    isValidating.value = true;

    try {
      // 执行基本的字段校验（不包括导航和资料检索）
      await performBasicFieldValidation(row, column, rowIndex, 'cell-switch');
    } finally {
      isValidating.value = false;
    }
  };

  // 处理跨行输入框切换时的校验逻辑
  const handleCellValidationForCrossRow = async (
    row: any,
    column: any,
    rowIndex: number,
    _newRowIndex: number | null,
    _newFieldName: string | null,
  ) => {
    // 防止重复处理
    if (isValidating.value) return;

    // 检查是否有Modal弹窗打开
    if (modalOpen.value || zljsModalOpen.value) return;

    isValidating.value = true;

    try {
      // 执行基本的字段校验
      const valid = await performBasicFieldValidation(row, column, rowIndex, 'cell-cross-row');

      // 如果校验失败，尝试回到原来的输入框
      if (
        !valid &&
        previousEditPosition.value.rowIndex !== null &&
        previousEditPosition.value.fieldName
      ) {
        setTimeout(() => {
          tableRef.value?.setEditCell(
            previousEditPosition.value.row,
            previousEditPosition.value.fieldName,
          );
        }, 100);
      }
    } finally {
      isValidating.value = false;
    }
  };

  // 基础字段校验函数（提取公共逻辑）
  const performBasicFieldValidation = async (
    row: any,
    column: any,
    rowIndex: number,
    validationMode: string,
  ): Promise<boolean> => {
    // 获取字段配置
    const { mxTableList } = templateStore.djInfo;
    const fieldConfig = Object.values(mxTableList).find(
      (config) => config.fieldName === column.field,
    );

    if (!fieldConfig) {
      return true;
    }

    // 字段类型校验
    if (fieldConfig.fldType) {
      const value = row[column.field];
      let isValid = true;
      let errorMessage = '';

      // 实数类型验证
      if (fieldConfig.fldType == 1 || fieldConfig.fldType === '1') {
        const regex = /^-?\d*\.?\d*$/;
        if (value && !regex.test(value)) {
          isValid = false;
          errorMessage = '请输入有效的数值';
          // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
          row[column.field] = null;
        }
      }
      // 整数类型验证
      else if (fieldConfig.fldType == 2 || fieldConfig.fldType === '2') {
        const regex = /^-?\d*$/;
        if (value && !regex.test(value)) {
          isValid = false;
          errorMessage = '请输入有效的整数';
          // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
          row[column.field] = null;
        }
      }
      // 字符类型验证
      else if (fieldConfig.fldType == 3 || fieldConfig.fldType === '3') {
        if (value && fieldConfig.fieldLength && String(value).length > fieldConfig.fieldLength) {
          isValid = false;
          errorMessage = `输入内容超出最大长度${fieldConfig.fieldLength}`;
          row[column.field] = String(value).substring(0, fieldConfig.fieldLength);
        }
      }

      // 如果验证不通过，显示错误
      if (!isValid && errorMessage) {
        Modal.warn({
          title: '校验错误',
          content: errorMessage,
          okText: '确定',
        });
        return false;
      }
    }

    // 构建校验参数
    const validateParams = await buildValidateParams(row, column, rowIndex, true);
    validateParams.validationMode = validationMode;

    try {
      const validateResult = await templateStore.validateField(validateParams);

      // 简化的校验结果处理（不包括导航逻辑）
      return new Promise<boolean>((resolve) => {
        handleValidationResult(validateResult, row, column, async (valid) => {
          // 触发挂账（只有在非数据检索期间才执行）
          if (valid && (fieldConfig as any).isSaveNow && !isDataRetrieving.value) {
            const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
            await templateStore.saveGzToServer(storageKey);
          } else {
            if (!isDataRetrieving.value) {
              templateStore.saveGz();
            } else {
              console.log('数据检索期间，跳过本地缓存保存（基础校验）');
            }
          }
          resolve(valid);
        });
      });
    } catch (error) {
      console.error('字段校验出错:', error);
      Modal.warn({
        title: '校验错误',
        content: error.message || '字段校验过程中发生错误',
        okText: '确定',
      });
      return false;
    }
  };

  // 处理单元格校验逻辑（失去焦点时触发）
  const handleCellValidation = async (row: any, column: any, rowIndex: number) => {
    // 防止重复处理
    if (isValidating.value) return;

    // 检查是否有Modal弹窗打开 - 修复逻辑错误：应该是 || 而不是 &&
    if (modalOpen.value || zljsModalOpen.value) return;

    isValidating.value = true;

    try {
      // 获取字段配置
      const { mxTableList } = templateStore.djInfo;
      const fieldConfig = Object.values(mxTableList).find(
        (config) => config.fieldName === column.field,
      );

      if (!fieldConfig) {
        return;
      }

      // 添加fldType字段类型校验
      if (fieldConfig.fldType) {
        const value = row[column.field];
        let isValid = true;
        let errorMessage = '';

        // 实数类型验证 (fldType = 1 或 '1')
        if (fieldConfig.fldType == 1 || fieldConfig.fldType === '1') {
          const regex = /^-?\d*\.?\d*$/;
          if (value && !regex.test(value)) {
            isValid = false;
            errorMessage = '请输入有效的数值';
            // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
            row[column.field] = null;
          }
        }
        // 整数类型验证 (fldType = 2 或 '2')
        else if (fieldConfig.fldType == 2 || fieldConfig.fldType === '2') {
          const regex = /^-?\d*$/;
          if (value && !regex.test(value)) {
            isValid = false;
            errorMessage = '请输入有效的整数';
            // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
            row[column.field] = null;
          }
        }
        // 字符类型验证 (fldType = 3 或 '3')
        else if (fieldConfig.fldType == 3 || fieldConfig.fldType === '3') {
          // 字符类型一般不需要特殊验证，但如果有长度限制可以在这里检查
          if (value && fieldConfig.fieldLength && String(value).length > fieldConfig.fieldLength) {
            isValid = false;
            errorMessage = `输入内容超出最大长度${fieldConfig.fieldLength}`;
            // 截断为允许的最大长度
            row[column.field] = String(value).substring(0, fieldConfig.fieldLength);
          }
        }

        // 如果验证不通过且无法自动修正，显示错误
        if (!isValid && row[column.field] !== value && errorMessage) {
          Modal.warn({
            title: '校验错误',
            content: errorMessage,
            okText: '确定',
          });
          return;
        }
      }

      // 构建校验参数（使用新的验证方式，指定操作类型为"输入框失焦"）
      const validateParams = await buildValidateParams(row, column, rowIndex, true);
      validateParams.validationMode = 'cell-blur'; // 设置验证模式为输入框失焦

      const validateResult = await templateStore.validateField(validateParams);
      // 处理校验结果（不包括导航逻辑）
      handleValidationResult(validateResult, row, column, async (valid) => {
        // 触发挂账（只有在非数据检索期间才执行）
        if (valid && fieldConfig.isSaveNow && !isDataRetrieving.value) {
          // 立即触发挂账
          const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
          await templateStore.saveGzToServer(storageKey);
        } else {
          // 非立即挂账字段仍然保存到本地缓存
          if (!isDataRetrieving.value) {
            templateStore.saveGz();
          } else {
            console.log('数据检索期间，跳过本地缓存保存（失去焦点）');
          }
        }

        isValidating.value = false;
      });
    } catch (error) {
      console.error('字段校验出错:', error);
      Modal.warn({
        title: '校验错误',
        content: error.message || '字段校验过程中发生错误',
        okText: '确定',
      });
    } finally {
      // 确保在任何情况下都重置 isValidating 状态
      isValidating.value = false;
    }
  };

  // 处理表格字段的按键事件
  const handleCellKeydown = async (params: any, event: KeyboardEvent, item: any) => {
    const { row, column, rowIndex } = params;
    lastClickColumn.value = column;
    console.log('handleCellKeydown', params, event, item);
    if (event && event.key === 'Enter') {
      // 防止重复处理
      if (isValidating.value) return;

      // 检查是否有Modal弹窗打开 - 修复逻辑错误：应该是 || 而不是 &&
      if (modalOpen.value || zljsModalOpen.value) return;

      isValidating.value = true;

      // 获取字段配置
      const { mxTableList } = templateStore.djInfo;
      const fieldConfig = Object.values(mxTableList).find(
        (config) => config.fieldName === column.field,
      );

      if (!fieldConfig) {
        isValidating.value = false;
        return;
      }

      // 添加fldType字段类型校验
      if (fieldConfig.fldType) {
        const value = row[column.field];
        let isValid = true;
        let errorMessage = '';

        // 实数类型验证 (fldType = 1 或 '1')
        if (fieldConfig.fldType == 1 || fieldConfig.fldType === '1') {
          const regex = /^-?\d*\.?\d*$/;
          if (value && !regex.test(value)) {
            isValid = false;
            errorMessage = '请输入有效的数值';
            // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
            row[column.field] = null;
          }
        }
        // 整数类型验证 (fldType = 2 或 '2')
        else if (fieldConfig.fldType == 2 || fieldConfig.fldType === '2') {
          const regex = /^-?\d*$/;
          if (value && !regex.test(value)) {
            isValid = false;
            errorMessage = '请输入有效的整数';
            // 数值类型字段统一设置为空值（显示为空，传递给后端时统一为0）
            row[column.field] = null;
          }
        }
        // 字符类型验证 (fldType = 3 或 '3')
        else if (fieldConfig.fldType == 3 || fieldConfig.fldType === '3') {
          // 字符类型一般不需要特殊验证，但如果有长度限制可以在这里检查
          if (value && fieldConfig.fieldLength && String(value).length > fieldConfig.fieldLength) {
            isValid = false;
            errorMessage = `输入内容超出最大长度${fieldConfig.fieldLength}`;
            // 截断为允许的最大长度
            row[column.field] = String(value).substring(0, fieldConfig.fieldLength);
          }
        }

        // 如果验证不通过且无法自动修正，显示错误并保持焦点
        if (!isValid && row[column.field] !== value && errorMessage) {
          isValidating.value = false;
          Modal.warn({
            title: '校验错误',
            content: errorMessage,
            okText: '确定',
            onOk: () => {
              // 确保焦点返回到当前单元格
              setTimeout(() => {
                tableRef.value?.setEditCell(row, column.field);
              }, 0);
            },
          });
          return;
        }
      }

      // 步骤1: 实数或整数类型校验（前端自动校验）
      // 使用新的验证方式，指定操作类型为"输入框间切换"
      const validateParams = buildValidateParams(row, column, rowIndex, true);
      validateParams.validationMode = 'cell-switch'; // 设置验证模式为输入框间切换

      try {
        const validateResult = await templateStore.validateField(validateParams);
        // 处理校验结果
        handleValidationResult(validateResult, row, column, async (valid) => {
          // 步骤3: 如果设置了资料检索，则触发资料检索
          if (valid && item.onEnterFunc && !zljsModalOpen.value) {
            dataChooseOption.value = 'Table';
            handleFormEvent(item.onEnterFunc, event, rowIndex, 1);
          } else {
            // 如果没有资料检索，则处理表格导航
            // 对于实数类型(fldType=1)的字段，确保在处理完业务逻辑后能够正确移动光标
            nextTick(() => {
              handleTableNavigation(row, column);
            });
          }
          // 步骤4: 触发挂账（只有在非数据检索期间才执行）
          if (valid && fieldConfig.isSaveNow && !isDataRetrieving.value) {
            // 立即触发挂账
            const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
            await templateStore.saveGzToServer(storageKey);
          } else {
            // 非立即挂账字段仍然保存到本地缓存
            if (!isDataRetrieving.value) {
              templateStore.saveGz();
            } else {
              console.log('数据检索期间，跳过本地缓存保存（回车键）');
            }
          }

          isValidating.value = false;
        });
      } catch (error) {
        console.error('字段校验出错:', error);
        isValidating.value = false;
        Modal.warn({
          title: '校验错误',
          content: error.message || '字段校验过程中发生错误',
          okText: '确定',
          onOk: () => {
            // 确保焦点返回到当前单元格
            setTimeout(() => {
              tableRef.value?.setEditCell(row, column.field);
            }, 0);
          },
        });
      }
    }
  };

  // 颜色解析函数：支持十六进制、RGB值或颜色名称
  function parseColor(colorValue: string | null | undefined): string | null {
    if (!colorValue || colorValue.trim() === '') {
      return null;
    }

    const color = colorValue.trim();

    // 十六进制颜色码（如：#FF0000, #f00, FF0000, f00）
    if (/^#?[0-9A-Fa-f]{3}$|^#?[0-9A-Fa-f]{6}$/.test(color)) {
      return color.startsWith('#') ? color : `#${color}`;
    }

    // RGB格式（如：rgb(255,0,0) 或 255,0,0）
    const rgbMatch = color.match(/^(?:rgb\()?(\d+),\s*(\d+),\s*(\d+)\)?$/);
    if (rgbMatch) {
      const [, r, g, b] = rgbMatch;
      return `rgb(${r}, ${g}, ${b})`;
    }

    // RGBA格式（如：rgba(255,0,0,0.5)）
    const rgbaMatch = color.match(/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)$/);
    if (rgbaMatch) {
      return color;
    }

    // 颜色名称（如：red, blue, green等）
    const colorNames = [
      'red',
      'blue',
      'green',
      'yellow',
      'orange',
      'purple',
      'pink',
      'brown',
      'black',
      'white',
      'gray',
      'grey',
      'cyan',
      'magenta',
      'lime',
      'navy',
      'maroon',
      'olive',
      'teal',
      'silver',
      'gold',
      'indigo',
      'violet',
    ];

    if (colorNames.includes(color.toLowerCase())) {
      return color.toLowerCase();
    }

    // 如果都不匹配，返回null
    return null;
  }

  // 为表头添加颜色渲染功能
  function addHeaderColorRender(item: any) {
    const backgroundColor = parseColor(item.backC);
    const textColor = parseColor(item.fldC);

    // 如果没有颜色配置，返回原始item
    if (!backgroundColor && !textColor) {
      return item;
    }

    // 创建自定义表头渲染函数
    const customHeaderRender = (column: any) => {
      const style: any = {};

      if (backgroundColor) {
        style.backgroundColor = backgroundColor;
      }

      if (textColor) {
        style.color = textColor;
      }

      // 添加一些基本样式确保表头显示正常
      style.padding = '8px 12px';
      style.fontWeight = 'bold';
      style.textAlign = 'center';
      style.borderRadius = '4px';

      return h(
        'div',
        {
          style,
          title: column.title || item.title, // 添加tooltip显示完整标题
        },
        column.title || item.title,
      );
    };

    return {
      ...item,
      customHeaderRender,
    };
  }

  function addEvents(columns) {
    return columns.map((item) => {
      // 首先添加颜色渲染功能
      let processedItem = addHeaderColorRender(item);

      // 处理日期/时间控件
      if (processedItem.datetype) {
        let format;

        console.log('处理日期字段:', processedItem.field, 'datetype:', processedItem.datetype);

        switch (processedItem.datetype) {
          case 'D':
            format = 'YYYY-MM-DD';
            break;
          case 'E':
            format = 'HH:mm:ss';
            break;
          case 'F':
            format = 'YYYY-MM-DD HH:mm:ss';
            break;
          default:
            break;
        }

        if (format) {
          // 使用日期选择器组件
          return {
            ...processedItem,
            datetype: processedItem.datetype,
            format: format,
            editRender: {
              name: 'ADatePicker',
              props: {
                placeholder: `请选择${format.includes('HH') ? '时间' : '日期'}`,
                valueFormat: format,
                format: format,
                showTime: format.includes('HH'),
                inputReadOnly: true,
                allowClear: true,
                style: { width: '100%' },
              },
              attrs: {
                autocomplete: 'off',
                'data-no-autocomplete': true,
                class: 'date-picker-cell',
              },
              immediate: true,
              events: {
                change(params, event) {
                  const { row, column } = params;
                  console.log('日期字段值变更:', column.field, row[column.field]);
                  handleTableChange();
                  // 确保保存的是正确格式的字符串
                  if (row[column.field]) {
                    if (typeof row[column.field] === 'object') {
                      if (row[column.field] instanceof Date) {
                        row[column.field] = dateUtil(row[column.field]).format(format);
                      } else if (row[column.field].format) {
                        // dayjs对象
                        row[column.field] = row[column.field].format(format);
                      }
                    } else if (
                      typeof row[column.field] === 'string' &&
                      row[column.field].includes('T')
                    ) {
                      // ISO格式字符串
                      row[column.field] = dateUtil(row[column.field]).format(format);
                    }
                  }

                  lastClickColumn.value = column;
                  if (processedItem.onChangeFunc)
                    handleFormEvent(processedItem.onChangeFunc, event, row, 1);
                },
                // 添加单元格激活时的处理，确保日期类型的一致性
                activated(params) {
                  const { row, column } = params;
                  if (row[column.field] && typeof row[column.field] === 'string') {
                    // 确保在激活编辑时，字符串日期被转换为dayjs对象，防止format不是函数的错误
                    try {
                      const dayjsValue = dateUtil(row[column.field]);
                      if (dayjsValue.isValid()) {
                        // 临时存储原始值
                        row._originalDateValue = row[column.field];
                        // 设置为dayjs对象供编辑器使用
                        row[column.field] = dayjsValue;
                      }
                    } catch (e) {
                      console.error('日期转换失败:', e);
                    }
                  }
                },
                // 添加单元格取消编辑时的处理，恢复原始格式
                deactivated(params) {
                  const { row, column } = params;
                  // 如果有原始值，则恢复
                  if (row._originalDateValue) {
                    row[column.field] = row._originalDateValue;
                    delete row._originalDateValue;
                  }
                },
                keydown: async (params, event) => {
                  handleCellKeydown(params, event, processedItem);
                },
              },
              cellRender: {
                name: '$format',
                props: {
                  formatDate: true,
                  dateFormat: format,
                },
              },
            },
          };
        }
      }

      // 处理下拉选择框
      if (
        processedItem.isDict &&
        processedItem.dropdownoptions &&
        processedItem.dropdownoptions.length > 0
      ) {
        // 排序下拉选项
        const sortedOptions = [...processedItem.dropdownoptions].sort((a, b) => a.sort - b.sort);

        // 构建下拉框渲染配置
        return {
          ...processedItem,
          editRender: {
            name: 'ASelect',
            options: sortedOptions.map((option) => ({
              label: option.name,
              value: option.name,
            })),
            props: {
              placeholder: `请选择${processedItem.title}`,
              optionLabelProp: 'value',
            },
            attrs: {
              autocomplete: 'off',
              'data-no-autocomplete': true,
            },
            immediate: true,
            events: {
              change(params, event) {
                const { row, column } = params;
                lastClickColumn.value = column;
                // 从选中项中获取value值
                if (row[column.field]) {
                  // 获取表格当前所有数据
                  const tableData = tableRef.value?.getTableData().fullData;
                  if (tableData && tableData.length > 0) {
                    // 找到当前行在表格数据中的索引
                    const rowIndex = tableData.findIndex((item) => item === row);
                    if (rowIndex !== -1) {
                      // 更新表格数据中对应行的数据
                      tableData[rowIndex] = { ...tableData[rowIndex], ...row };

                      // 更新templateStore中的表格数据
                      templateStore.setTemplateForm({
                        ...templateStore.templateForm,
                        mxtable: tableData,
                      });

                      // 保存数据到本地缓存
                      templateStore.saveGz();

                      // 设置下一个焦点位置
                      nextFocus(mxTableRow, lastClickColumn.value, true);
                    }
                  }
                }

                // handleTableChange();
                // if (item.onChangeFunc) handleFormEvent(item.onChangeFunc, event, row, 1);
              },
              keydown: async (params, event) => {
                handleCellKeydown(params, event, processedItem);
              },
            },
          },
          // 使用formatter属性处理显示内容
          formatter({ cellValue }) {
            if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
              const option = sortedOptions.find((opt) => String(opt.value) === String(cellValue));
              return option ? option.name : cellValue;
            }
            return '';
          },
          cellRender({ row, column }) {
            const field = column.field as string;
            const cellValue = row[field];
            if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
              const option = sortedOptions.find((opt) => String(opt.value) === String(cellValue));
              return option ? option.name : cellValue;
            }
            return '';
          },
        };
      }

      // 为实数类型字段添加数值格式化显示
      if (processedItem.fldType === '1' || processedItem.fldType === 1) {
        const baseItem = {
          ...processedItem,
          // 添加数值格式化显示
          formatter({ cellValue }) {
            return formatNumberByFldDec(cellValue, processedItem.fldDec);
          },
          cellRender({ row, column }) {
            const field = column.field as string;
            const cellValue = row[field];
            return formatNumberByFldDec(cellValue, processedItem.fldDec);
          },
        };

        if (!processedItem.editRender) return baseItem;

        return {
          ...baseItem,
          editRender: {
            ...processedItem.editRender,
            props: {
              ...processedItem.editRender?.props,
              autocomplete: 'off',
            },
            attrs: {
              ...processedItem.editRender?.attrs,
              autocomplete: 'off',
              'data-no-autocomplete': true,
            },
            immediate: true,
            events: {
              change(params, event) {
                const { row, column } = params;
                lastClickColumn.value = column;
                if (processedItem.onChangeFunc)
                  handleFormEvent(processedItem.onChangeFunc, event, row, 1);
              },
              dblclick(params, event) {
                const { row, column, rowIndex } = params;
                lastClickColumn.value = column;
                if (processedItem.onDblFunc)
                  handleFormEvent(processedItem.onDblFunc, event, rowIndex, 1);
              },
              focus(params, event) {
                const { row, column, rowIndex } = params;
                lastClickColumn.value = column;
              },
              keydown: async (params, event) => {
                handleCellKeydown(params, event, processedItem);
              },
            },
          },
        };
      }

      if (!processedItem.editRender) return processedItem;
      return {
        ...processedItem,
        editRender: {
          ...processedItem.editRender,
          props: {
            ...processedItem.editRender?.props,
            autocomplete: 'off',
          },
          attrs: {
            ...processedItem.editRender?.attrs,
            autocomplete: 'off',
            'data-no-autocomplete': true,
          },
          immediate: true,
          events: {
            change(params, event) {
              const { row, column } = params;
              lastClickColumn.value = column;
              if (processedItem.onChangeFunc)
                handleFormEvent(processedItem.onChangeFunc, event, row, 1);
            },
            dblclick(params, event) {
              const { row, column, rowIndex } = params;
              lastClickColumn.value = column;
              if (processedItem.onDblFunc)
                handleFormEvent(processedItem.onDblFunc, event, rowIndex, 1);
            },
            focus(params, event) {
              const { row, column, rowIndex } = params;
              lastClickColumn.value = column;
            },
            keydown: async (params, event) => {
              handleCellKeydown(params, event, processedItem);
            },
          },
        },
      };
    });
  }

  function addFilter(columns) {
    return columns.map((item) => {
      if (!item.field) return item;
      // if (!item.editRender) return item;
      return {
        ...item,
        // 输入框变化事件
        icon: '',
        showIcon: false,
        children: [
          {
            ...item,
            width: item.width,
            filters: [{ data: '', label: item.title, checked: false, showIcon: false }],
            filterMethod: ({ option, row, column }) => {
              if (option.data) {
                const cellValue = row[column.field] ?? '';
                return String(cellValue).toLowerCase().includes(String(option.data).toLowerCase());
              }
              return true;
            },
            slots: {
              header: `header_${item.field}`,
            },
          },
        ],
      };
    });
  }
  function closeFilter() {
    addedFilter.value = !addedFilter.value;
    gridOptions.columns = gridOptions.columns.map((item: any) => {
      if (!item.field) return item;
      return {
        ...item,
        // 输入框变化事件
        children: [],
      };
    });
  }

  function addDisable(columns) {
    return columns.map((item) => {
      if (!item.field) return item;
      if (!item.editRender) return item;
      return {
        ...item,
        // 输入框变化事件
        editRender: {
          ...item.editRender,
        },
      };
    });
  }

  function calcMxtable(mxtable) {
    const { mxTableList } = templateStore.djInfo || {};

    // 筛选出有表达式内容的字段，并按 computerOrder 排序
    const formatList = (mxTableList || [])
      .filter((item) => item.expContent && item.expContent.trim() !== '')
      .sort((a, b) => {
        // 先按 computerOrder 排序，null/undefined 值放到最后
        const orderA = a.computerOrder != null ? Number(a.computerOrder) : Number.MAX_SAFE_INTEGER;
        const orderB = b.computerOrder != null ? Number(b.computerOrder) : Number.MAX_SAFE_INTEGER;

        if (orderA !== orderB) {
          return orderA - orderB;
        }

        // 如果 computerOrder 相同，按字段名排序保证一致性
        return (a.fieldName || '').localeCompare(b.fieldName || '');
      });

    console.log(
      '表达式字段按 computerOrder 排序结果:',
      formatList.map((item) => ({
        fieldName: item.fieldName,
        computerOrder: item.computerOrder,
        expContent: item.expContent,
      })),
    );

    if (mxtable && mxtable.length > 0) {
      mxtable.forEach((row, rowIndex) => {
        // 按照 computerOrder 顺序依次计算表达式
        formatList.forEach((item) => {
          try {
            const oldValue = row[item.fieldName];
            // 判断templateStore是否有设置临时的表达式，如果有则使用临时表达式，否则使用原表达式
            const newValue = calculateExpContent(item.expContent?.toLowerCase(), row);

            if (oldValue !== newValue) {
              console.log(
                `第${rowIndex + 1}行 ${item.fieldName}(order:${item.computerOrder}) 计算: ${oldValue} -> ${newValue}`,
              );
            }

            row[item.fieldName] = newValue;
          } catch (error) {
            console.error(
              `第${rowIndex + 1}行字段 ${item.fieldName}(order:${item.computerOrder}) 表达式计算失败:`,
              error,
            );
            // 计算失败时设置为0，避免影响后续计算
            row[item.fieldName] = 0;
          }
        });

        // 处理临时表达式（保持原有逻辑）
        const tempExpContent = templateStore.tempExpContent;
        if (tempExpContent) {
          Object.keys(tempExpContent).forEach((key) => {
            try {
              row[key] = calculateExpContent(tempExpContent[key].toLowerCase(), row);
            } catch (error) {
              console.error(`临时表达式字段 ${key} 计算失败:`, error);
              row[key] = 0;
            }
          });
        }
      });
    }
    return mxtable;
  }

  /**
   * 计算所有汇总项（包括表头和表尾的lx为S或P的项目）
   * 确保所有汇总项都被计算，无论是否显示
   */
  function calcAllSummaryItems() {
    const { kbxTableList } = templateStore.djInfo || {};
    const { mxtable } = templateStore.templateForm || {};

    if (!kbxTableList || !mxtable || mxtable.length === 0) {
      return;
    }

    // 计算表头汇总项（lx为S或P的项目），按 computerOrder 排序
    const summaryItems = kbxTableList
      .filter((item) => item.lx === 'S' || item.lx === 'P')
      .sort((a, b) => {
        // 先按 computerOrder 排序，null/undefined 值放到最后
        const orderA = a.computerOrder != null ? Number(a.computerOrder) : Number.MAX_SAFE_INTEGER;
        const orderB = b.computerOrder != null ? Number(b.computerOrder) : Number.MAX_SAFE_INTEGER;

        if (orderA !== orderB) {
          return orderA - orderB;
        }

        // 如果 computerOrder 相同，按字段名排序保证一致性
        return (a.fieldName || '').localeCompare(b.fieldName || '');
      });

    console.log(
      '汇总项按 computerOrder 排序结果:',
      summaryItems.map((item) => ({
        fieldName: item.fieldName,
        lx: item.lx,
        computerOrder: item.computerOrder,
        expContent: item.expContent,
      })),
    );

    summaryItems.forEach((item) => {
      if (item.expContent && item.expContent.trim() !== '') {
        // 如果有表达式，使用表达式计算
        // 对于表头汇总项，使用整个明细表数据进行计算
        const summaryRow = createSummaryRowFromMxtable(mxtable);
        const result = calculateExpContent(item.expContent.toLowerCase(), summaryRow);

        // 根据lx类型设置值
        if (item.lx === 'P') {
          // P类型转换为大写金额，应用 fldDec 精度控制
          templateStore.templateForm.kbxtable[item.fieldName] =
            CurrencyFormatter.toChineseUppercase(result, item.fldDec);
        } else {
          // S类型保持数值
          templateStore.templateForm.kbxtable[item.fieldName] = result;
        }
      } else {
        // 如果没有表达式，根据字段名从明细表中求和
        const sum = mxtable.reduce((total, row) => {
          const value = Number(row[item.fieldName]) || 0;
          return total + value;
        }, 0);

        // 根据lx类型设置值
        if (item.lx === 'P') {
          // P类型转换为大写金额，应用 fldDec 精度控制
          templateStore.templateForm.kbxtable[item.fieldName] =
            CurrencyFormatter.toChineseUppercase(sum, item.fldDec);
        } else {
          // S类型保持数值
          templateStore.templateForm.kbxtable[item.fieldName] = sum;
        }
      }
    });

    console.log(
      '汇总项计算完成:',
      summaryItems.map((item) => ({
        fieldName: item.fieldName,
        lx: item.lx,
        value: templateStore.templateForm.kbxtable[item.fieldName],
      })),
    );
  }

  /**
   * 从明细表数据创建汇总行，用于表达式计算
   */
  function createSummaryRowFromMxtable(mxtable) {
    const summaryRow = {};

    if (!mxtable || mxtable.length === 0) {
      return summaryRow;
    }

    // 获取所有字段名
    const allFields = new Set();
    mxtable.forEach((row) => {
      Object.keys(row).forEach((key) => allFields.add(key));
    });

    // 对每个字段进行求和
    allFields.forEach((fieldName) => {
      if (fieldName === '_X_ROW_KEY') return; // 跳过内部字段

      const sum = mxtable.reduce((total, row) => {
        const value = Number(row[fieldName]) || 0;
        return total + value;
      }, 0);

      summaryRow[fieldName] = sum;
    });

    return summaryRow;
  }

  function confirmFilterEvent(option, column) {
    const filter = column.filters[0];
    filter.checked = true;
    tableRef.value?.updateData();
  }

  function calculateExpContent(expContent: string, row: Record<string, any>): number {
    try {
      // 替换变量
      const expression = expContent.replace(/([a-zA-Z]+)/g, (match) => {
        // 检查row中是否存在该变量，存在则返回其值，不存在则返回0
        return row[match] !== undefined ? String(row[match]) : '0';
      });

      // 检查表达式是否有数字后直接跟括号的情况（如"0(..."）并修复
      const safeExpression = expression.replace(/(\d)(\()/g, '$1 * $2');
      const result = new Function(`return ${safeExpression}`)();

      // 将结果转换为数字
      const numResult = Number(result);

      // 检查是否为有效数字且不为无穷大
      if (!isNaN(numResult) && isFinite(numResult)) {
        // 不再强制保留2位小数，保持计算结果的原始精度
        // 显示格式化由表格的formatter函数根据字段的fldDec配置处理
        return numResult;
      } else {
        return 0;
      }
    } catch (error) {
      console.error('表达式计算错误:', error);
      return 0;
    }
  }

  /**
   * 强制重新计算所有表达式字段
   * 用于在挂账和存盘前确保所有数值都是最新的
   */
  function forceRecalculateAllExpressions() {
    try {
      console.log('开始强制重新计算所有表达式字段...');

      // 获取当前表格数据
      const tableData = tableRef.value?.getTableData().fullData || [];
      const { mxTableList } = templateStore.djInfo || {};

      if (!mxTableList || tableData.length === 0) {
        console.log('无表格数据或配置，跳过表达式重新计算');
        return;
      }

      // 获取所有包含表达式的字段，并按 computerOrder 排序
      const expressionFields = mxTableList
        .filter((item) => item.expContent && item.expContent.trim() !== '')
        .sort((a, b) => {
          // 先按 computerOrder 排序，null/undefined 值放到最后
          const orderA =
            a.computerOrder != null ? Number(a.computerOrder) : Number.MAX_SAFE_INTEGER;
          const orderB =
            b.computerOrder != null ? Number(b.computerOrder) : Number.MAX_SAFE_INTEGER;

          if (orderA !== orderB) {
            return orderA - orderB;
          }

          // 如果 computerOrder 相同，按字段名排序保证一致性
          return (a.fieldName || '').localeCompare(b.fieldName || '');
        });

      if (expressionFields.length === 0) {
        console.log('没有找到包含表达式的字段');
        return;
      }

      console.log(
        '找到表达式字段(按computerOrder排序):',
        expressionFields.map((f) => `${f.fieldName}(order:${f.computerOrder}): ${f.expContent}`),
      );

      // 标记是否有数据被更新
      let hasUpdated = false;

      // 遍历每一行数据重新计算表达式
      tableData.forEach((row, rowIndex) => {
        // 检查是否为有效数据行（排除空行）
        const hasValidData = Object.entries(row).some(([key, value]) => {
          if (key === '_X_ROW_KEY' || key === 'dj_rec') return false;
          return value !== null && value !== undefined && value !== '';
        });

        if (!hasValidData) {
          return; // 跳过空行
        }

        // 按照 computerOrder 顺序依次计算表达式
        expressionFields.forEach((field) => {
          try {
            const oldValue = row[field.fieldName];
            const newValue = calculateExpContent(field.expContent.toLowerCase(), row);

            if (oldValue !== newValue) {
              console.log(
                `第${rowIndex + 1}行 ${field.fieldName}(order:${field.computerOrder}) 值更新: ${oldValue} -> ${newValue}`,
              );
              row[field.fieldName] = newValue;
              hasUpdated = true;
            }
          } catch (error) {
            console.error(
              `计算第${rowIndex + 1}行字段 ${field.fieldName}(order:${field.computerOrder}) 表达式失败:`,
              error,
            );
          }
        });
      });

      // 如果有数据更新，刷新表格显示
      if (hasUpdated) {
        console.log('表达式重新计算完成，刷新表格显示');
        tableRef.value?.loadData([...tableData]);

        // 同时重新计算汇总项
        calcAllSummaryItems();
      } else {
        console.log('表达式重新计算完成，无数据变更');
      }
    } catch (error) {
      console.error('强制重新计算表达式字段时发生错误:', error);
    }
  }

  function transformColumnData(data: any) {
    // 添加防御性检查，确保 data 不为空
    if (!data) return [];

    // 使用解构赋值时提供默认空对象，避免 undefined 错误
    const { resizableData = {}, sortData = {}, visibleData = {} } = data;
    const flds = [];

    console.log('转换列宽和顺序数据:', data);

    // 处理排序数据
    for (const [fieldName, order] of Object.entries(sortData)) {
      if (fieldName === 'type=checkbox') continue;
      const field: {
        fieldname: string;
        displayorder: number;
        customlength?: number;
      } = {
        fieldname: fieldName,
        displayorder: Number(order) || 0, // 确保是数字，防止 NaN
      };

      // 使用可选链和空值合并操作符防止 undefined 错误
      if (resizableData?.[fieldName]) {
        field.customlength = Number(resizableData[fieldName]) || 0;
      }

      flds.push(field);
    }

    console.log('转换后的列数据:', flds);
    return flds; // 返回字段列表，API需要接收一个列表
  }

  function handleContainerClick() {
    // 不再需要控制抽屉
  }

  function handleFormChange() {
    // 计算汇总项
    calcAllSummaryItems();
    templateStore.saveGz();
  }

  async function handleMenuClick(menu) {
    const row = tableRef.value?.getCurrentRecord();

    let menuCodeLower = menu.code?.toLowerCase() ?? '';
    // 识别是否是Excel导入菜单
    if (menuCodeLower.startsWith('excelimp_')) {
      currentExcelImportMenuCode.value = menu.code;
      excelImportModalOpen.value = true;
      return;
    }

    // 识别是否是商品档案查询菜单
    if (menuCodeLower === 'getdspinfo') {
      if (!row || !row.dspid) {
        Modal.warn({
          title: '查询错误',
          content: '未找到商品编号，请先选择商品',
          okText: '确定',
        });
        return;
      }

      try {
        // 调用档案资料查询接口
        const response = await queryArchiveDataApi({ dspid: row.dspid });

        // 保存数据
        ziliaoData.value = response;
        // 打开详情弹窗
        ziliaoModalOpen.value = true;
      } catch (error) {
        console.error('商品档案查询失败:', error);
        Modal.warn({
          title: '查询错误',
          content: '商品档案查询失败，请稍后重试',
          okText: '确定',
        });
      }
      return;
    }

    // 识别是否是往来单位档案查询菜单
    if (menuCodeLower === 'getddwinfo') {
      // 从非明细项(XForm)获取ddwid字段的值
      const ddwid = templateStore.templateForm.kbxtable.ddwid;

      if (!ddwid) {
        Modal.warn({
          title: '查询错误',
          content: '未找到单位编号，请先选择往来单位',
          okText: '确定',
        });
        return;
      }

      try {
        // 调用档案资料查询接口
        const response = await queryArchiveDataApi({ ddwid });

        // 保存数据
        ziliaoData.value = response;
        // 打开详情弹窗
        ziliaoModalOpen.value = true;
      } catch (error) {
        console.error('往来单位档案查询失败:', error);
        Modal.warn({
          title: '查询错误',
          content: '往来单位档案查询失败，请稍后重试',
          okText: '确定',
        });
      }
      return;
    }

    // 识别是否是文件管理菜单
    if (menuCodeLower === 'djlxfilecomm') {
      if (!templateStore.djlx || !templateStore.gzbh) {
        Modal.warn({
          title: '操作错误',
          content: '缺少必要参数，请确保单据已保存',
          okText: '确定',
        });
        return;
      }

      // 打开文件管理弹窗
      fileManagementModalOpen.value = true;
      return;
    }
    console.log('menu.code', menuCodeLower);
    switch (menuCodeLower) {
      case 'showfullscreen':
        tableRef.value?.zoom();
        break;
      case 'showcolumn':
        if (addedFilter.value) {
          closeFilter();
        }
        setTimeout(() => {
          const $table = tableRef.value?.$refs.tableElRef;
          if ($table) {
            // 打开列设置弹窗前，先刷新表格列配置
            tableRef.value?.refreshColumn();

            // 确保列设置弹窗支持拖拽
            if ($table.openCustom) {
              $table.openCustom();
              console.log('已打开列设置弹窗，支持拖动调整顺序');

              // 监听列设置变化事件，确保保存到服务器
              const saveColumnSettings = () => {
                // 列设置保存是由 customConfig.updateStore 自动处理的
                console.log('列设置已变更，将自动保存');
                // 刷新列显示
                tableRef.value?.refreshColumn();
              };

              // 列设置弹窗关闭时触发
              const customCloseHandler = () => {
                saveColumnSettings();
                // 移除事件监听器
                document.removeEventListener('click', checkCustomClose);
              };

              // 检查是否关闭了列设置弹窗
              const checkCustomClose = (e) => {
                const customWrapper = document.querySelector('.vxe-custom--wrapper');
                if (!customWrapper || !customWrapper.contains(e.target)) {
                  customCloseHandler();
                  document.removeEventListener('click', checkCustomClose);
                }
              };

              // 添加一个延迟后监听点击事件
              setTimeout(() => {
                document.addEventListener('click', checkCustomClose);
              }, 300);
            } else {
              console.error('表格实例没有openCustom方法');
            }
          } else {
            console.error('无法获取表格实例');
          }
        }, 100);
        break;
      case 'menudel':
        // 判断是否有选择行
        const checkedData = tableRef.value?.getCheckboxRecords();
        const tableData = tableRef.value?.getTableData().fullData;

        // 如果没有选中任何行，提示用户并返回
        if (!checkedData || checkedData.length === 0) {
          // 保存当前活跃元素
          const activeElement = document.activeElement as HTMLElement;
          const modal = Modal.warn({
            title: '提示',
            content: '请先选择要删除的行',
            onOk: () => {
              // 延迟一帧后将焦点返回到之前的元素
              setTimeout(() => {
                if (activeElement) {
                  activeElement.focus();
                }
              }, 0);
            },
          });

          // 添加自定义键盘事件处理
          const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              modal.destroy();
              document.removeEventListener('keydown', handleKeyDown);
              // 延迟一帧后将焦点返回到之前的元素
              setTimeout(() => {
                if (activeElement) {
                  activeElement.focus();
                }
              }, 0);
            }
          };
          document.addEventListener('keydown', handleKeyDown);
          return;
        }

        // 分离空行和非空行
        const emptyRows: any[] = [];
        const nonEmptyRows: any[] = [];

        checkedData.forEach((row) => {
          // 检查该行是否为空行（除了内部字段外都是null、undefined或空字符串）
          const isEmpty = Object.keys(row).every((key) => {
            // 排除内部字段：_X_ROW_KEY 是表格内部标识，dj_rec 是系统自动生成的行号
            // 这些字段不算作用户编辑的数据
            if (key === 'dj_rec' || key === '_X_ROW_KEY') return true;
            const value = row[key];
            // 只有当值为null、undefined或空字符串时才认为该字段为空
            // 数字0也算作有效数据，不应该被认为是空
            return value === null || value === undefined || value === '';
          });

          if (isEmpty) {
            emptyRows.push(row);
          } else {
            nonEmptyRows.push(row);
          }
        });

        // 如果有空行，直接删除
        if (emptyRows.length > 0) {
          await performDelete(emptyRows, null, tableRef.value?.getTableData().fullData);
        }

        // 如果没有非空行需要校验，直接返回
        if (nonEmptyRows.length === 0) {
          return;
        }

        // 构建校验请求数据（只对非空行进行校验）
        const checkData = nonEmptyRows.map((item) => {
          // 获取当前行在表格中的实际索引
          const actualRowIndex = tableData?.indexOf(item) ?? -1;
          return {
            djlxbs: templateStore.djlx,
            gzbh: templateStore.gzbh,
            dj_rec: item.dj_rec || (actualRowIndex > -1 ? actualRowIndex + 1 : 1), // 优先使用行内存储的dj_rec值
          };
        });

        try {
          // 调用删除校验接口
          const checkResults = await djDeleteCheckApi(checkData);
          // 处理校验结果
          const hasBlockingError = checkResults.some((result) => result.pass === -1);
          const hasWarning = checkResults.some((result) => result.pass === 1);

          if (hasBlockingError) {
            // 存在不允许删除的记录（pass = -1）
            const errorMessages = checkResults
              .filter((result) => result.pass === -1)
              .map((result) => result.title)
              .filter(Boolean);

            // 保存当前活跃元素
            const activeElement = document.activeElement as HTMLElement;
            const modal = Modal.warn({
              title: '删除失败',
              content: errorMessages.join('\n') || '不允许删除',
              onOk: () => {
                // 延迟一帧后将焦点返回到之前的元素
                setTimeout(() => {
                  if (activeElement) {
                    activeElement.focus();
                  }
                }, 0);
              },
            });

            // 添加自定义键盘事件处理
            const handleKeyDown = (e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                modal.destroy();
                document.removeEventListener('keydown', handleKeyDown);
                // 延迟一帧后将焦点返回到之前的元素
                setTimeout(() => {
                  if (activeElement) {
                    activeElement.focus();
                  }
                }, 0);
              }
            };
            document.addEventListener('keydown', handleKeyDown);
            return;
          }

          if (hasWarning) {
            // 有需要提示的记录（pass = 1）
            const warningMessages = checkResults
              .filter((result) => result.pass === 1)
              .map((result) => result.title)
              .filter(Boolean);

            Modal.confirm({
              title: '删除确认',
              content: warningMessages.join('\n') || '是否确认删除？',
              okText: '继续',
              cancelText: '返回',
              onOk: async () => {
                // 用户选择继续，执行删除操作
                await performDelete(checkedData, null, tableData);
              },
            });
            return;
          }

          // 其他情况（pass = 0 或其他值），直接删除
          await performDelete(checkedData, null, tableData);
        } catch (error) {
          console.error('删除校验失败:', error);
          // 保存当前活跃元素
          const activeElement = document.activeElement as HTMLElement;
          const modal = Modal.warn({
            title: '删除失败',
            content: '删除校验时发生错误',
            onOk: () => {
              // 延迟一帧后将焦点返回到之前的元素
              setTimeout(() => {
                if (activeElement) {
                  activeElement.focus();
                }
              }, 0);
            },
          });

          // 添加自定义键盘事件处理
          const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
              modal.destroy();
              document.removeEventListener('keydown', handleKeyDown);
              // 延迟一帧后将焦点返回到之前的元素
              setTimeout(() => {
                if (activeElement) {
                  activeElement.focus();
                }
              }, 0);
            }
          };
          document.addEventListener('keydown', handleKeyDown);
        }
        break;
      case 'menuadd':
        // 检查是否已存在空行，如果存在则不新增
        if (!hasEmptyRow()) {
          tableRef.value?.insertAt({}, -1);
          // 更新行编号
          updateRowDjRec();
        }
        break;
      case 'copydata':
        const rowData = { ...row, _X_ROW_KEY: Math.random() };
        tableRef.value?.insertAt(rowData, -1);
        tableRef.value?.setCheckboxRow(rowData, true);
        break;
      case 'pmnrecsel':
        tableRef.value?.setCheckboxRow(row, true);
        break;
      case 'allrecsel':
        tableRef.value?.setAllCheckboxRow(true);
        break;
      case 'norecsel':
        tableRef.value?.setAllCheckboxRow(false);
        tableRef.value?.clearCurrentRow();
        break;
      case 'recfind':
        if (!addedFilter.value) {
          gridOptions.columns = addFilter(gridOptions.columns);
          addedFilter.value = !addedFilter.value;
          return;
        }
        break;
      case 'jefirst':
        handleExpEvent(1);
        break;
      case 'sefirst':
        handleExpEvent(2);
        break;
      case 'djfirst':
        handleExpEvent(3);
        break;
      case 'excelimp_orderinput':
        // EXCEL导入功能
        if (menuCodeLower.startsWith('excelimp_')) {
          currentExcelImportMenuCode.value = menu.code;
          excelImportModalOpen.value = true;
          return;
        }
        break;
      case 'getddwinfo':
        // 单位详情已在函数开头处理
        break;
      case 'getdspinfo':
        // 商品详情已在函数开头处理
        break;
      case 'djlxfilecomm':
        // 附件管理功能
        fileManagementModalOpen.value = true;
        break;
      default:
        // 保存当前活跃元素
        const activeElement = document.activeElement as HTMLElement;
        const modal = Modal.success({
          title: '操作提示',
          content: `点击了 ${menu.code}`,
          okText: '确定',
          onOk: () => {
            // 延迟一帧后将焦点返回到之前的元素
            setTimeout(() => {
              if (activeElement) {
                activeElement.focus();
              }
            }, 0);
          },
        });

        // 添加自定义键盘事件处理
        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === 'Enter') {
            modal.destroy();
            document.removeEventListener('keydown', handleKeyDown);
            // 延迟一帧后将焦点返回到之前的元素
            setTimeout(() => {
              if (activeElement) {
                activeElement.focus();
              }
            }, 0);
          }
        };
        document.addEventListener('keydown', handleKeyDown);
        break;
    }
  }

  // 处理表格区域的键盘事件
  function handleTableKeydown(event: KeyboardEvent) {
    // 检查当前焦点是否在表格区域内
    const isInTableArea = mainTableContainer.value?.contains(document.activeElement);
    if (!isInTableArea) return;

    if (event.key === 'Home' || event.key === 'End') {
      const $table = tableRef.value;
      if ($table) {
        const $el = $table.$el;
        const scrollContainer = $el.querySelector('.vxe-table--body-wrapper');
        if (scrollContainer) {
          if (event.key === 'Home') {
            scrollContainer.scrollTo({ left: 0, behavior: 'smooth' });
          } else {
            scrollContainer.scrollTo({
              left: scrollContainer.scrollWidth - scrollContainer.clientWidth,
              behavior: 'smooth',
            });
          }
          event.preventDefault();
        }
      }
    }

    // 添加上下键导航
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      // 获取当前处于编辑状态的单元格信息
      const $table = tableRef.value;
      if (!$table) return;

      // 获取当前活动元素，检查它是否是表格中的输入框
      const activeElement = document.activeElement as HTMLElement;
      if (
        !activeElement ||
        !activeElement.tagName ||
        !['INPUT', 'TEXTAREA'].includes(activeElement.tagName)
      ) {
        return;
      }

      const cell = activeElement.closest('.vxe-cell');
      if (!cell) return;

      // 找到当前单元格所在行和列
      const row = cell.closest('.vxe-body--row');
      const column = cell.closest('.vxe-body--column');
      if (!row || !column) return;

      // 获取行索引 - VxeTable可能使用不同的属性名
      let rowIndex = -1;

      // 尝试几种可能的属性名
      const rowIndexAttr =
        row.getAttribute('row-index') ||
        row.getAttribute('data-rowid') ||
        row.getAttribute('data-row-index');

      if (rowIndexAttr) {
        rowIndex = parseInt(rowIndexAttr);
      } else {
        // 如果没有找到属性，尝试从DOM位置确定
        const rows = Array.from(row.parentElement?.children || []);
        rowIndex = rows.indexOf(row);
      }

      if (rowIndex === -1) return;

      // 根据索引获取行数据和列配置
      const tableData = $table.getTableData().fullData;
      if (!tableData || rowIndex >= tableData.length) return;

      const currentRow = tableData[rowIndex];

      // 尝试多种方法获取列字段名
      let fieldName = '';

      // 方法1：从cell的data-field属性获取
      if (cell.dataset && cell.dataset.field) {
        fieldName = cell.dataset.field;
      }

      // 方法2：从input元素的name或id属性获取
      if (!fieldName) {
        const input =
          activeElement.tagName === 'INPUT' ? activeElement : cell.querySelector('input');
        if (input) {
          fieldName =
            (input as HTMLInputElement).name ||
            (input as HTMLInputElement).id ||
            (input as HTMLInputElement).getAttribute('data-field') ||
            '';
        }
      }

      // 方法3：从column元素的属性获取
      if (!fieldName) {
        fieldName = column.getAttribute('data-field') || column.getAttribute('field') || '';
      }

      // 方法4：获取列索引，然后从gridOptions.columns中查找
      if (!fieldName) {
        // 尝试获取列索引
        let colIndex = -1;
        const colIndexAttr =
          column.getAttribute('col-index') ||
          column.getAttribute('data-colid') ||
          column.getAttribute('data-col-index');

        if (colIndexAttr) {
          colIndex = parseInt(colIndexAttr);
        } else {
          // 如果没有找到属性，尝试从DOM位置确定
          const parentRow = column.parentElement;
          if (parentRow) {
            const columns = Array.from(parentRow.children || []);
            colIndex = columns.indexOf(column);
          }
        }

        // 如果找到了有效的列索引，从gridOptions.columns中获取字段名
        if (colIndex >= 0 && colIndex < gridOptions.columns.length) {
          const col = gridOptions.columns[colIndex];
          if (col && col.field) {
            fieldName = col.field;
          }
        }
      }

      // 如果仍然没有找到字段名，无法继续
      if (!fieldName) {
        console.error('无法确定列字段名', {
          cell,
          column,
          row,
          activeElement,
        });
        return;
      }

      // 添加调试日志
      console.log('导航信息:', {
        direction: event.key,
        rowIndex,
        fieldName,
        currentRow,
      });

      // 阻止默认行为
      event.preventDefault();

      // 处理上下键导航
      if (event.key === 'ArrowUp') {
        // 向上导航：获取上一行
        if (rowIndex > 0) {
          const prevRow = tableData[rowIndex - 1];
          // 导航到上一行相同列
          $table.setEditCell(prevRow, fieldName);
        }
      } else if (event.key === 'ArrowDown') {
        // 向下导航：获取下一行
        if (rowIndex < tableData.length - 1) {
          const nextRow = tableData[rowIndex + 1];
          // 导航到下一行相同列
          $table.setEditCell(nextRow, fieldName);
        } else {
          // 如果是最后一行并且不是空行，则创建新行
          if (!isRowEmpty(currentRow) && !hasEmptyRow()) {
            // 添加新行
            $table.insertAt({}, -1);

            // 更新行号
            updateRowDjRec();

            // 获取新行
            const newRow = $table.getTableData().fullData.slice(-1)[0];

            // 设置焦点到新行的相同列
            nextTick(() => {
              $table.setEditCell(newRow, fieldName);
            });
          }
        }
      }
    }
  }

  // 在组件挂载时添加事件监听
  onMounted(() => {
    window.addEventListener('resize', handleResize);
    // 添加键盘事件监听
    if (mainTableContainer.value) {
      mainTableContainer.value.addEventListener('keydown', handleTableKeydown);
    }
  });

  // 在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    // 移除键盘事件监听
    if (mainTableContainer.value) {
      mainTableContainer.value.removeEventListener('keydown', handleTableKeydown);
    }

    // 清理所有活跃的Modal弹窗，防止跨页面显示
    activeModals.value.forEach((modal) => {
      try {
        modal.destroy();
      } catch (error) {
        console.warn('清理Modal时出错:', error);
      }
    });
    activeModals.value.clear();

    // 清理校验锁
    validationLock.value.clear();

    // 重置校验状态
    isValidating.value = false;
  });

  // 添加handleClearAll函数
  function handleClearAll() {
    Modal.confirm({
      title: '清空确认',
      content: '确定要清空所有内容吗？此操作不可恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 保存当前重要状态，以便清空后恢复
          const djlx = templateStore.djlx;
          const djbs = templateStore.djbs;
          const currentFuncInfo = { ...templateStore.funcinfo }; // 深拷贝函数信息
          const currentHandleFuncName = handleFuncName.value; // 保存当前处理函数名
          const currentEventType = cachedEventType.value; // 保存事件类型

          // 保存当前挂账编号
          const existingGzbh = templateStore.gzbh;
          const bizId = query.bizId;

          // 清除服务器挂账
          if (existingGzbh) {
            await templateStore.clearGz([existingGzbh], bizId); // 传入bizId参数
          }
          // 清除本地缓存
          const storageKey = `gz_${djlx}`;
          const existingDataStr = localStorage.getItem('gz_data') || '{}';
          const existingData = JSON.parse(existingDataStr);
          if (existingData[storageKey]) {
            delete existingData[storageKey];
            localStorage.setItem('gz_data', JSON.stringify(existingData));
          }

          // 获取初始值
          const response = await getInitValueApi(djlx, 0);

          templateStore.setGzInfo({
            djlx,
            djbs,
            templateForm: {
              kbxtable: {},
              mxtable: [{}],
            },
          });
          const initValues = response || {};
          // 重置表单,保留默认值
          const formData = {};
          djInfo.value.kbxTableList
            .filter(
              (i) => i.itemRender.name !== 'AButton' && i.itemRender.name !== 'AText' && i.isActive,
            )
            .forEach((item) => {
              // 如果是初始值则保留,否则设为null
              formData[item.fieldName] = initValues[item.fieldName] || null;
            });

          xFormRef.value.setFieldsValue(formData);

          // 重置表格数据为一个空行
          const emptyRow = {};
          // 为明细表的数值型字段设置默认值
          djInfo.value.mxTableList?.forEach((item) => {
            // 如果是表达式字段，不设置默认值（让表达式计算结果决定）
            if (item.expContent && item.expContent.trim() !== '') {
              // 表达式字段先设置为null，后续由表达式计算
              emptyRow[item.field] = null;
              return;
            }

            // 所有字段都设置为null，不再设置默认值0
            emptyRow[item.field] = null;
          });
          tableRef.value?.loadData([emptyRow]);

          // 重置子表格
          sonMxGridOptions.value = null;

          // 清空勾兑方式记录
          gouduiMethodMap.value.clear();
          // 持久化清除操作
          saveGouduiMethodsToStorage();

          templateStore.setTemplateForm({
            kbxtable: { ...formData, ...initValues },
            mxtable: [{}],
          });
          // 清除挂账编号等状态
          templateStore.setGzbh('');

          // 恢复重要状态
          templateStore.setFuncInfo(currentFuncInfo); // 恢复函数信息
          handleFuncName.value = currentHandleFuncName; // 恢复处理函数名
          cachedEventType.value = currentEventType; // 恢复事件类型

          // 保存到localStorage
          await templateStore.saveGz();

          // 调用后端保存
          await templateStore.saveGzToServer(storageKey, false);

          Modal.success({
            title: '清空成功',
            content: '已清空非默认值内容',
            okText: '确定',
            onOk: () => {
              // 成功提示关闭后再设置焦点，避免焦点被覆盖
              nextTick(() => {
                xFormRef.value.jumpToFirstInput();
              });
            },
          });
        } catch (err) {
          console.error('清空操作失败:', err);
          Modal.warn({
            title: '清空失败',
            content: err.message || '清空操作失败',
            okText: '确定',
            onOk: () => {
              // 即使发生错误，也尝试恢复焦点
              nextTick(() => {
                xFormRef.value.jumpToFirstInput();
              });
            },
          });
        }
      },
    });
  }

  // 获取上一个可编辑单元格
  function getPreviousEditableCell(currentRow, currentColumn) {
    const columnIndex = gridOptions.columns.findIndex((col) => col.field === currentColumn.field);
    let prevEditableColumnIndex = columnIndex - 1;

    // 在当前行查找上一个可编辑列
    while (prevEditableColumnIndex >= 0) {
      if (gridOptions.columns[prevEditableColumnIndex].editRender?.name === 'AInput') {
        return {
          row: currentRow,
          column: gridOptions.columns[prevEditableColumnIndex],
        };
      }
      prevEditableColumnIndex--;
    }

    // 如果当前行没有找到,查找上一行的最后一个可编辑列
    const rowIndex = tableRef.value?.getTableData().fullData.indexOf(currentRow);
    if (rowIndex > 0) {
      const prevRow = tableRef.value?.getTableData().fullData[rowIndex - 1];
      const lastEditableColumn = gridOptions.columns
        .filter((col) => col?.editRender?.name === 'AInput')
        .slice(-1)[0];
      if (prevRow[lastEditableColumn.field]) {
        return {
          row: prevRow,
          column: lastEditableColumn,
        };
      }
    }

    return null;
  }

  // 添加打印方案选择处理函数
  /**
   * 处理打印方案选择
   * @param scheme 选中的打印方案
   */
  async function handlePrintSchemeSelect(scheme: PrintSchemeVo) {
    try {
      // 显示全屏加载
      const hide = message.loading({
        content: '正在准备打印预览...',
        duration: 0,
        class: 'custom-loading-message',
      });
      debugger;
      // 获取单据相关信息
      // 优先使用保存单据后返回的挂账编号，如果没有则使用原单据编号
      const danjbh = savedGzbh.value || templateStore.templateForm.danjbh;
      const faid = scheme.faid;

      // 关闭打印方案选择弹窗
      printSchemeModalOpen.value = false;

      // 获取SVG格式的ZIP文件
      const blob = await exportSvgZip({
        djlx: scheme.dataid,
        type: '0', // 0-单据
        dataid: scheme.dataid, // type为0时传djlx
        faid: faid,
        paramMap: {
          danjbh: danjbh, // 传递挂账编号或单据编号
        },
      });
      // 使用JSZip解压缩
      const zip = new jszip();
      const zipContent = await zip.loadAsync(blob);

      // 获取所有SVG文件
      const svgFiles: string[] = [];

      // 遍历ZIP文件内容
      for (const filename in zipContent.files) {
        if (filename.endsWith('.svg')) {
          const fileContent = await zipContent.files[filename].async('text');
          svgFiles.push(fileContent);
        }
      }

      if (svgFiles.length === 0) {
        hide();
        message.error('没有找到可打印的SVG文件');
        return;
      }

      // 创建打印容器
      const printContainer = document.createElement('div');
      printContainer.style.display = 'none';
      document.body.appendChild(printContainer);

      // 添加所有SVG到打印容器
      svgFiles.forEach((svgContent) => {
        printContainer.innerHTML += svgContent;
      });

      // 打印完成后的清理函数
      const cleanUp = () => {
        document.body.removeChild(printContainer);
        hide();
      };

      // 等待图像加载完成
      setTimeout(() => {
        try {
          // 打印内容
          const printWindow = window.open('', '_blank');
          if (!printWindow) {
            throw new Error('无法创建打印窗口');
          }

          printWindow.document.write(`
            <html>
              <head>
                <title>打印预览</title>
                <style>
                  @media print {
                    body { margin: 0; padding: 0; }
                    svg { page-break-after: always; }
                  }
                </style>
              </head>
              <body>${printContainer.innerHTML}</body>
            </html>
          `);

          // 等待内容加载完成后打印
          printWindow.document.close();
          printWindow.onload = () => {
            printWindow.print();
            printWindow.onafterprint = () => {
              printWindow.close();
              cleanUp();
            };
          };

          // 如果onload没有触发，设置超时
          setTimeout(() => {
            if (printWindow.document.readyState === 'complete') {
              printWindow.print();
              printWindow.onafterprint = () => {
                printWindow.close();
                cleanUp();
              };
            }
          }, 1000);
        } catch (err) {
          console.error('打印失败:', err);
          message.error('打印失败，请稍后重试');
          cleanUp();
        }
      }, 500);
    } catch (err) {
      console.error('处理打印任务失败:', err);
      message.error('处理打印任务失败，请稍后重试');
      printSchemeModalOpen.value = false;
    }
  }

  // 在文件末尾添加 performDelete 函数
  async function performDelete(
    checkedData: any[] | undefined,
    row: any,
    tableData: any[] | undefined,
  ) {
    // 打印删除明细行的debug日志
    console.log('🗑️ [DEBUG] 右键删除明细行', {
      timestamp: new Date().toISOString(),
      checkedData: checkedData,
      singleRow: row,
      tableDataLength: tableData?.length || 0,
      deletedRowsCount: checkedData?.length || (row ? 1 : 0),
    });

    // 在删除前确保每行都有正确的dj_rec值
    if (checkedData?.length !== 0 && tableData) {
      checkedData.forEach((item) => {
        if (!item.dj_rec) {
          const actualRowIndex = tableData.indexOf(item);
          item.dj_rec = actualRowIndex + 1;
        }
      });
    } else if (row && tableData) {
      if (!row.dj_rec) {
        const actualRowIndex = tableData.indexOf(row);
        row.dj_rec = actualRowIndex + 1;
      }
    }

    if (checkedData?.length !== 0) {
      if (tableData?.length > 1) {
        tableRef.value?.remove(checkedData);
        if (tableRef.value?.getTableData().fullData.length === 0) {
          tableRef.value?.insertAt({}, -1);
        }
      } else {
        const emptyRow = {};
        tableRef.value?.loadData([emptyRow]);
      }
    } else if (row) {
      if (tableData?.length > 1) {
        tableRef.value?.remove(row);
      } else {
        const emptyRow = {};
        tableRef.value?.loadData([emptyRow]);
      }
    }

    // 删除后更新所有行的dj_rec编号
    updateRowDjRec();

    templateStore.setTemplateForm({
      ...templateStore.templateForm,
      mxtable: tableRef.value?.getTableData().fullData,
    });
    templateStore.saveGz();

    // 删除明细行后调用挂账
    try {
      const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
      await templateStore.saveGzToServer(storageKey);
    } catch (error) {
      console.error('删除明细行后挂账失败:', error);
    }
  }

  // 监听弹窗打开状态
  watch(zljsModalOpen, (newVal, oldVal) => {
    // 当弹窗从打开状态变为关闭状态时
    if (oldVal === true && newVal === false) {
      // 锁定回车事件处理300ms
      enterEventLock.value = true;
      setTimeout(() => {
        enterEventLock.value = false;
      }, 500); // 设置500ms的锁定期，避免回车事件连续触发
    }
  });

  /**
   * 处理打印功能，由ClearGz组件触发
   */
  async function print() {
    try {
      // 获取单据类型
      const djlx = query.djlx as string;
      // 获取打印方案列表，业务类型设置为 0（表示单据类型）
      debugger;
      const response = await getPrintSchemes('0', djlx);
      printSchemes.value = response;

      // 根据打印方案数量决定处理方式
      if (response.length === 0) {
        message.error('没有找到可用的打印方案');
      } else if (response.length === 1) {
        // 只有一个打印方案，直接选择并执行打印
        await handlePrintSchemeSelect(response[0]);
      } else {
        // 多个打印方案，显示弹窗让用户选择
        printSchemeModalOpen.value = true;
      }
    } catch (err) {
      console.error('获取打印方案列表失败:', err);
      message.error('获取打印方案列表失败，请稍后重试');
    }
  }

  // 在表格校验函数中添加格式和长度限制逻辑
  const validateTableData = (row, column) => {
    if (!row || !column || !column.field) return true;

    try {
      // 获取字段配置
      const { mxTableList } = templateStore.djInfo;
      const fieldConfig = Object.values(mxTableList).find(
        (config) => config.fieldName === column.field,
      );

      if (!fieldConfig) {
        return true;
      }

      // 添加fldType字段类型校验
      if (fieldConfig.fldType) {
        const value = row[column.field];
        let isValid = true;
        let errorMessage = '';

        // 实数类型验证 (fldType = 1 或 '1')
        if (fieldConfig.fldType == 1 || fieldConfig.fldType === '1') {
          const regex = /^-?\d*\.?\d*$/;
          if (value && !regex.test(value)) {
            isValid = false;
            errorMessage = '请输入有效的数字';
          }
        }
        // 整数类型验证 (fldType = 2 或 '2')
        else if (fieldConfig.fldType == 2 || fieldConfig.fldType === '2') {
          const regex = /^-?\d*$/;
          if (value && !regex.test(value)) {
            isValid = false;
            errorMessage = '请输入有效的整数';
          }
        }
        // 字符类型验证 (fldType = 3 或 '3')
        else if (fieldConfig.fldType == 3 || fieldConfig.fldType === '3') {
          // 字符类型一般不需要特殊验证，但如果有长度限制可以在这里检查
          if (value && fieldConfig.fieldLength && String(value).length > fieldConfig.fieldLength) {
            isValid = false;
            errorMessage = `输入内容超出最大长度${fieldConfig.fieldLength}`;
            // 截断为允许的最大长度
            row[column.field] = String(value).substring(0, fieldConfig.fieldLength);
          }
        }

        if (!isValid) {
          tableRef.value?.updateStatus({
            row,
            column,
            valid: false,
            message: errorMessage,
          });
          return false;
        }

        // 检查长度和小数位数 (针对数字类型)
        if ((fieldConfig.fldType == 1 || fieldConfig.fldType === '1') && value) {
          // 处理小数位数限制 - 只验证不修改数据
          if (fieldConfig.fldDec !== undefined && fieldConfig.fldDec >= 0) {
            const parts = String(value).split('.');
            if (parts.length > 1 && parts[1].length > fieldConfig.fldDec) {
              // 如果小数位数超出限制，给出警告但不强制截断数据
              console.warn(
                `字段 ${column.field} 小数位数超出配置限制 ${fieldConfig.fldDec} 位，当前输入：${value}`,
              );
              // 只在显示时进行格式化，不修改原始数据
              // 数据的格式化显示由 formatNumberByFldDec 函数在 formatter 中处理
            }
          }

          // 处理总长度限制
          if (fieldConfig.fieldLength) {
            const strValue = String(value).replace(/\./g, '');
            if (strValue.length > fieldConfig.fieldLength) {
              isValid = false;
              errorMessage = `输入数字总长度不能超过${fieldConfig.fieldLength}位`;
              tableRef.value?.updateStatus({
                row,
                column,
                valid: false,
                message: errorMessage,
              });
              return false;
            }
          }
        }
      }

      // 验证通过
      tableRef.value?.clearValidate();
      return true;
    } catch (error) {
      console.error('验证数据时出错:', error);
      return true;
    }
  };

  function handleFooterSumChange(sumData: any) {
    // 获取字段、值、类型和小数位数
    const { field, value, type, decimals = 2 } = sumData;

    // 确保kbxtable存在
    if (!templateStore.templateForm.kbxtable) {
      templateStore.templateForm.kbxtable = {};
    }

    // 根据类型设置对应的值，确保小数位数一致
    if (type === 'numeric') {
      // 数字类型：使用统一的格式化函数
      templateStore.templateForm.kbxtable[field] = CurrencyFormatter.formatNumber(value, decimals);
    } else if (type === 'chinese') {
      // 中文大写类型：使用统一的格式化函数
      templateStore.templateForm.kbxtable[field] = CurrencyFormatter.toChineseUppercase(
        value,
        decimals,
      );
    } else {
      // 默认情况：直接保存值
      templateStore.templateForm.kbxtable[field] = value;
    }

    console.log(`[金额汇总] 字段: ${field}, 类型: ${type}, 值: ${value}, 小数位数: ${decimals}`);

    // 保存到localStorage
    saveToLocalStorage();
  }

  // 保存到localStorage的函数
  function saveToLocalStorage() {
    // 使用防抖的保存方法
    debouncedSave();
  }

  // 处理勾兑方案保存
  function handleGouduiSave(data: any[]) {
    // 更新表格数据
    if (tableRef.value && data && data.length > 0) {
      // 如果是明细表行数据更新
      if (mxTableRow.value !== null && mxTableRow.value !== undefined) {
        const tableData = tableRef.value.getTableData().fullData;
        if (tableData && tableData[mxTableRow.value]) {
          // 更新指定行的数据
          Object.assign(tableData[mxTableRow.value], data[0]);
          // 重新加载表格数据
          tableRef.value.loadData([...tableData]);
        }
      }

      // 保存数据到本地缓存
      templateStore.saveGz();
      message.success('勾兑数据已成功保存');
    }
  }

  watch(
    () => templateStore.gzbh,
    (newVal, oldVal) => {
      console.log(
        `[GZBH LOG] templateStore.gzbh 变化:`,
        oldVal,
        '=>',
        newVal,
        new Date().toISOString(),
      );
      if (newVal === '') {
        console.warn('[GZBH LOG] templateStore.gzbh 被清空', new Date().toISOString());
      }
    },
    { immediate: true },
  );

  // 处理表格中的表达式字段
  async function handleExpressionFields(tableData: any[], rowIndices: number[] = []) {
    if (!tableData || !templateStore.djInfo?.mxTableList) return;

    const { mxTableList } = templateStore.djInfo;
    // 筛选表达式字段并按 computerOrder 排序
    const expressionFields = Object.values(mxTableList)
      .filter((field: any) => field.expContent && field.expContent.trim() !== '')
      .sort((a: any, b: any) => {
        // 先按 computerOrder 排序，null/undefined 值放到最后
        const orderA = a.computerOrder != null ? Number(a.computerOrder) : Number.MAX_SAFE_INTEGER;
        const orderB = b.computerOrder != null ? Number(b.computerOrder) : Number.MAX_SAFE_INTEGER;

        if (orderA !== orderB) {
          return orderA - orderB;
        }

        // 如果 computerOrder 相同，按字段名排序保证一致性
        return (a.fieldName || '').localeCompare(b.fieldName || '');
      });

    if (expressionFields.length === 0) return;

    console.log(
      '处理表达式字段(按computerOrder排序):',
      expressionFields.map((f: any) => ({
        fieldName: f.fieldName,
        computerOrder: f.computerOrder,
        expContent: f.expContent,
      })),
    );

    // 如果没有指定行索引，则处理所有行
    const indicesToProcess =
      rowIndices.length > 0 ? rowIndices : Array.from(Array(tableData.length).keys());

    for (const rowIndex of indicesToProcess) {
      const row = tableData[rowIndex];
      if (!row) continue;

      // 按照 computerOrder 顺序依次计算表达式
      for (const field of expressionFields) {
        try {
          // 使用现有的calculateExpContent函数进行表达式计算
          const result = calculateExpContent(field.expContent?.toLowerCase(), row);
          if (result !== undefined) {
            row[field.fieldName] = result;
          }
        } catch (error) {
          console.error(
            `计算表达式字段 ${field.fieldName}(order:${field.computerOrder}) 失败:`,
            error,
          );
        }
      }
    }

    return tableData;
  }

  /**
   * 验证汇总项计算结果的准确性
   */
  function validateSummaryItems(): boolean {
    const { kbxTableList } = templateStore.djInfo || {};
    const { mxtable, kbxtable } = templateStore.templateForm || {};

    if (!kbxTableList || !mxtable || mxtable.length === 0) {
      return true;
    }

    let isValid = true;
    const errors: string[] = [];

    // 验证表头汇总项（lx为S或P的项目）
    const summaryItems = kbxTableList.filter((item) => item.lx === 'S' || item.lx === 'P');

    summaryItems.forEach((item) => {
      const currentValue = kbxtable[item.fieldName];

      // 重新计算预期值
      let expectedValue;
      if (item.expContent && item.expContent.trim() !== '') {
        // 表达式计算
        const summaryRow = createSummaryRowFromMxtable(mxtable);
        const result = calculateExpContent(item.expContent.toLowerCase(), summaryRow);
        expectedValue =
          item.lx === 'P' ? CurrencyFormatter.toChineseUppercase(result, item.fldDec) : result;
      } else {
        // 字段求和
        const sum = mxtable.reduce((total, row) => {
          const value = Number(row[item.fieldName]) || 0;
          return total + value;
        }, 0);
        expectedValue =
          item.lx === 'P' ? CurrencyFormatter.toChineseUppercase(sum, item.fldDec) : sum;
      }

      // 比较当前值和预期值
      if (item.lx === 'P') {
        // P类型比较字符串
        if (String(currentValue) !== String(expectedValue)) {
          errors.push(
            `汇总项【${item.fieldTitle || item.fieldName}】计算错误，当前值：${currentValue}，预期值：${expectedValue}`,
          );
          isValid = false;
        }
      } else {
        // S类型比较数值
        const currentNum = Number(currentValue) || 0;
        const expectedNum = Number(expectedValue) || 0;
        if (Math.abs(currentNum - expectedNum) > 0.01) {
          errors.push(
            `汇总项【${item.fieldTitle || item.fieldName}】计算错误，当前值：${currentNum}，预期值：${expectedNum}`,
          );
          isValid = false;
        }
      }
    });

    if (!isValid) {
      console.error('汇总项验证失败:', errors);
    } else {
      console.log('汇总项验证通过');
    }

    return isValid;
  }
</script>

<style lang="less" scoped>
  .invoice-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 136px); // 减去顶部菜单和padding的高度
    min-height: 0;
    padding: 0 8px;
    background-color: #fff;
    gap: 8px; // 使用gap替代margin-bottom
  }

  // 添加表头背景色样式
  :deep(.vxe-table--header) {
    background-color: #fff !important;

    .vxe-header--row {
      background-color: #fff !important;
    }
  }

  .header-section {
    flex-shrink: 0;
    border-radius: 2px;
  }

  .tables-section {
    display: flex;
    z-index: 10;
    flex: 1 1 auto;
    flex-direction: column;
    min-height: 200px;
    padding: 8px;
    padding-top: 0;
    border-radius: 2px;
    background: #fff;

    .flex {
      height: 100%; // 确保flex容器占满整个高度
    }

    // 子明细表格样式
    .son-mx-table {
      height: 100% !important;

      :deep(.vxe-table--main-wrapper) {
        height: 100% !important;
      }
    }

    // 确保两边表格高度一致
    :deep(.vxe-grid) {
      height: 100% !important;
    }

    // 调整工具栏样式保持一致
    :deep(.vxe-grid--toolbar-wrapper) {
      height: 36px !important;
      padding: 0 10px !important;
      line-height: 36px !important;
    }
  }

  // 确保子表格高度与父表格一致
  :deep(.vxe-table--main-wrapper) {
    height: 100%;
  }

  .main-table-wrapper {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0;

    :deep(.vxe-table) {
      flex: 1;
      overflow: auto;
    }
  }

  .table-divider {
    width: 100%;
    height: 1px;
    margin: 8px 0;
    background-color: #e8e8e8;
  }

  .sub-table-wrapper {
    height: 150px;
    overflow: hidden;
    transition: height 0.3s ease;
  }

  .footer-section {
    z-index: 9;
    flex-shrink: 0;
    height: 50px;
    padding: 0 8px;
    border-radius: 2px;
  }

  :deep(.vben-page-wrapper-content) {
    margin: 0;
  }

  :deep(.vxe-grid) {
    padding: 0;
    background-color: #fff !important;

    .vxe-toolbar {
      padding: 3px;
    }

    .vxe-header--row:nth-child(2) {
      height: 60px !important;

      // 调整单元格内部样式
      .vxe-header--column {
        padding: 0 !important; // 移除内边距

        .vxe-cell {
          height: 100% !important; // 确保单元格内容区域占满高度
          padding: 4px !important; // 给单元格内容添加合适的内边距
        }

        // 调整输入框容器
        .vxe-cell--edit {
          height: 100% !important;
          padding: 4px !important;

          // 调整输入框本身
          .ant-input,
          input {
            height: calc(100% - 8px) !important; // 减去padding空间
            margin: 4px 0 !important;
            line-height: normal !important;
          }
        }
      }
    }
  }
  // 确保输入框显示完整
  :deep(.vxe-cell--edit) {
    .ant-input,
    input {
      width: 100% !important;
      padding: 2px 8px !important;
    }
  }

  :deep(.ant-btn-link) {
    color: #000;
  }

  :deep(.col--filter .vxe-cell--edit-icon) {
    display: none !important;
  }

  :deep(.vxe-body--row:has(.vxe-cell--checkbox.is--checked)) {
    background-color: rgb(242 248 255) !important;
  }

  .main-table {
    border: none !important;
    border-right: none !important;
  }

  .son-mx-table-container {
    border: none !important;
    border-left: none !important;
  }

  :deep(.son-mx-table-container) {
    border: none !important;
    border-right: none !important;
  }

  :deep(.main-table) {
    border: none !important;
    border-right: none !important;
  }

  .nav-menu-container {
    display: flex;
    position: sticky;
    z-index: 10;
    top: 0;
    align-items: center;
    width: 100%;
    height: 32px;
    padding: 0 8px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;

    .menu-group {
      display: flex;
      align-items: center;
      gap: 4px;

      /* 分隔线 */
      &:not(:last-child)::after {
        content: '';
        width: 1px;
        height: 16px;
        margin: 0 4px;
        background: #f0f0f0;
      }
    }

    :deep(.ant-btn-link) {
      height: 32px;
      padding: 0 3px;
      transition: all 0.3s;
      border-radius: 4px;
      color: #595959;
      font-size: 14px;

      &:hover {
        color: rgb(188 78 39);
      }

      .anticon {
        margin-right: 4px;
        font-size: 14px;
      }

      /* 覆盖 antd 的默认间距 */
      &.ant-btn > .anticon + span,
      &.ant-btn > span + .anticon {
        margin-inline-start: 0 !important;
      }
    }
  }

  .mxBtn {
    &:hover {
      color: rgb(188 78 39);
    }
  }
</style>
